import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/sentence_model.dart';
import 'learning_path_session_service.dart';

/// خدمة لإدارة الجمل في مسار التعلم
class LearningPathSentenceService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final LearningPathSessionService _sessionService =
      LearningPathSessionService();

  // مفتاح لتخزين جمل المجموعة الحالية في Hive
  static String getLearningPathGroupKey(int levelId, int cycleId, int groupId) {
    return 'learning_path_level_${levelId}_cycle_${cycleId}_group_$groupId';
  }

  LearningPathSentenceService({
    required dynamic hiveSentenceService,
  });

  /// الحصول على جمل مجموعة معينة في مسار التعلم
  Future<List<SentenceModel>> getLearningPathGroupSentences(
      int levelId, int cycleId, int groupId,
      {bool forceRefresh = false}) async {
    try {
      debugPrint(
          'الحصول على جمل المجموعة $groupId في الدورة $cycleId من المستوى $levelId');

      // أولاً، تحقق من وجود جلسة محفوظة
      if (!forceRefresh) {
        final savedSession = await _sessionService.getBatchSession(
          levelId: levelId,
          cycleId: cycleId,
          groupId: groupId,
        );

        if (savedSession != null) {
          final sentences = savedSession['sentences'] as List<SentenceModel>;
          debugPrint('تم استعادة ${sentences.length} جملة من الجلسة المحفوظة');
          return sentences;
        }
      }

      // إذا لم توجد جلسة محفوظة أو تم طلب تحديث إجباري، جلب جمل جديدة
      debugPrint('جلب جمل جديدة من Firebase...');

      // تحديد مستوى الصعوبة بناءً على معرف المستوى
      String difficulty;
      switch (levelId) {
        case 1:
          difficulty = 'easy';
          break;
        case 2:
          difficulty = 'medium';
          break;
        case 3:
          difficulty = 'hard';
          break;
        default:
          difficulty = 'medium';
      }

      debugPrint('جلب جمل بمستوى صعوبة: $difficulty');

      // جلب الجمل المستخدمة سابقاً لتجنب التكرار
      final user = FirebaseAuth.instance.currentUser;
      Set<String> usedSentenceIds = {};

      if (user != null) {
        // جلب جميع الجمل المقروءة للمستخدم في هذا المستوى
        final readSentencesSnapshot = await _firestore
            .collection('users')
            .doc(user.uid)
            .collection('readSentences')
            .where('levelId', isEqualTo: levelId)
            .get();

        usedSentenceIds =
            readSentencesSnapshot.docs.map((doc) => doc.id).toSet();
        debugPrint(
            'تم العثور على ${usedSentenceIds.length} جملة مستخدمة سابقاً');
      }

      // جلب جمل من Firestore بناءً على مستوى الصعوبة
      final Query query = _firestore
          .collection('sentences')
          .where('difficulty', isEqualTo: difficulty)
          .limit(100); // زيادة الحد للحصول على خيارات أكثر

      final sentencesSnapshot = await query.get();

      if (sentencesSnapshot.docs.isEmpty) {
        debugPrint('لا توجد جمل متاحة بمستوى صعوبة $difficulty في Firestore');
        // إذا لم تكن هناك جمل بهذا المستوى، نجلب جمل عشوائية
        final fallbackSnapshot =
            await _firestore.collection('sentences').limit(50).get();

        if (fallbackSnapshot.docs.isEmpty) {
          debugPrint('لا توجد جمل متاحة في Firestore على الإطلاق');
          return [];
        }

        // تحويل الجمل إلى نموذج SentenceModel
        List<SentenceModel> fallbackSentences = [];
        for (var doc in fallbackSnapshot.docs) {
          final data = doc.data();
          fallbackSentences.add(SentenceModel(
            id: doc.id,
            englishText: data['englishText'] ?? '',
            arabicText: data['arabicText'] ?? '',
            category: data['category'] ?? 'عام',
            difficulty: data['difficulty'] ?? 'medium',
            createdAt:
                (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
            readBy: {},
            isFavorite: false,
          ));
        }

        // خلط الجمل واختيار 10 جمل
        fallbackSentences.shuffle(Random());
        final selectedFallbackSentences = fallbackSentences.take(10).toList();

        return selectedFallbackSentences;
      }

      // تحويل الجمل إلى نموذج SentenceModel وتصفية المستخدمة
      List<SentenceModel> allSentences = [];
      for (var doc in sentencesSnapshot.docs) {
        // تجنب الجمل المستخدمة سابقاً
        if (usedSentenceIds.contains(doc.id)) {
          debugPrint('تجاهل الجملة المستخدمة: ${doc.id}');
          continue;
        }

        final data = doc.data() as Map<String, dynamic>;
        allSentences.add(SentenceModel(
          id: doc.id,
          englishText: data['englishText'] ?? '',
          arabicText: data['arabicText'] ?? '',
          category: data['category'] ?? 'عام',
          difficulty: data['difficulty'] ?? 'medium',
          createdAt:
              (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
          readBy: {},
          isFavorite: false,
        ));
      }

      debugPrint('تم تصفية ${allSentences.length} جملة غير مستخدمة');

      // إذا لم تكن هناك جمل كافية، جلب جمل إضافية
      if (allSentences.length < 10) {
        debugPrint('جمل غير كافية، جلب جمل إضافية بدون تصفية...');

        final additionalQuery = _firestore
            .collection('sentences')
            .where('difficulty', isEqualTo: difficulty)
            .limit(50);

        final additionalSnapshot = await additionalQuery.get();

        for (var doc in additionalSnapshot.docs) {
          if (allSentences.length >= 10) break;

          // تجنب الجمل الموجودة بالفعل
          if (allSentences.any((s) => s.id == doc.id)) continue;

          final data = doc.data();
          allSentences.add(SentenceModel(
            id: doc.id,
            englishText: data['englishText'] ?? '',
            arabicText: data['arabicText'] ?? '',
            category: data['category'] ?? 'عام',
            difficulty: data['difficulty'] ?? 'medium',
            createdAt:
                (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
            readBy: {},
            isFavorite: false,
          ));
        }
      }

      // خلط الجمل واختيار 10 جمل
      allSentences.shuffle(Random());
      final selectedSentences = allSentences.take(10).toList();

      debugPrint('تم اختيار ${selectedSentences.length} جملة جديدة للمجموعة');

      // حفظ الجمل في الجلسة
      await _sessionService.saveBatchSession(
        levelId: levelId,
        cycleId: cycleId,
        groupId: groupId,
        sentences: selectedSentences,
        currentPosition: 0,
      );

      return selectedSentences;
    } catch (e) {
      debugPrint('خطأ في الحصول على جمل المجموعة: $e');
      return [];
    }
  }

  /// تحديث حالة قراءة جملة في مجموعة معينة
  Future<void> markSentenceAsRead(
    String sentenceId,
    int levelId,
    int cycleId,
    int groupId,
  ) async {
    try {
      // إضافة الجملة إلى قائمة الجمل المقروءة في Firebase
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // طباعة معلومات الجملة للتصحيح
        debugPrint('تعليم الجملة كمقروءة: $sentenceId');
        debugPrint('المستوى: $levelId, الدورة: $cycleId, المجموعة: $groupId');

        // تخزين المعلومات كأرقام صحيحة
        await _firestore
            .collection('users')
            .doc(user.uid)
            .collection('readSentences')
            .doc(sentenceId)
            .set({
          'readAt': FieldValue.serverTimestamp(),
          'levelId': levelId,
          'cycleId': cycleId,
          'groupId': groupId,
        });

        // إزالة الجملة من الجلسة الحالية
        await _sessionService.removeSentenceFromSession(
          levelId: levelId,
          cycleId: cycleId,
          groupId: groupId,
          sentenceId: sentenceId,
        );

        // التحقق من نجاح العملية
        final doc = await _firestore
            .collection('users')
            .doc(user.uid)
            .collection('readSentences')
            .doc(sentenceId)
            .get();

        if (doc.exists) {
          final data = doc.data();
          debugPrint('تم تخزين الجملة بنجاح: $data');
        } else {
          debugPrint('فشل في تخزين الجملة!');
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحديث حالة قراءة الجملة: $e');
    }
  }

  /// حذف جلسة مكتملة
  Future<void> clearCompletedSession(
    int levelId,
    int cycleId,
    int groupId,
  ) async {
    await _sessionService.clearSession(
      levelId: levelId,
      cycleId: cycleId,
      groupId: groupId,
    );
  }

  /// التحقق من وجود جلسة نشطة
  Future<bool> hasActiveSession(
    int levelId,
    int cycleId,
    int groupId,
  ) async {
    return await _sessionService.hasActiveSession(
      levelId: levelId,
      cycleId: cycleId,
      groupId: groupId,
    );
  }
}
