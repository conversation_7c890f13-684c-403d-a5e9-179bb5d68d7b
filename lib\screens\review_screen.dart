import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_tts/flutter_tts.dart';
import '../models/sentence_model.dart';
import '../models/lesson_group.dart';
import '../models/cycle.dart' show Cycle;
import '../providers/level_provider.dart';
import '../providers/points_provider.dart';
import '../models/points.dart';
import '../screens/conversation_detail_screen.dart';
import '../widgets/quiz_dialog.dart';
import '../widgets/points_animation.dart';
import '../services/learning_path_session_service.dart';

/// شاشة مراجعة الجمل المقروءة سابقاً
class ReviewScreen extends StatefulWidget {
  static const routeName = '/review';

  final int levelId;
  final int cycleId;
  final int groupId;
  final String title;
  final bool showGroups; // إظهار بطاقات المجموعات بدلاً من الجمل مباشرة

  const ReviewScreen({
    Key? key,
    required this.levelId,
    required this.cycleId,
    required this.groupId,
    required this.title,
    this.showGroups = false,
  }) : super(key: key);

  @override
  State<ReviewScreen> createState() => _ReviewScreenState();
}

class _ReviewScreenState extends State<ReviewScreen> {
  final PageController _pageController = PageController();
  bool _isLoading = true;
  List<SentenceModel> _reviewSentences = [];
  int _completedSentences = 0;
  double _accuracy = 0.0;
  int _totalSentences = 0;
  final List<Map<String, dynamic>> _groupsToReview = [];
  final FlutterTts _flutterTts = FlutterTts();
  bool _isSpeaking = false;
  final LearningPathSessionService _sessionService =
      LearningPathSessionService();

  /// أسترجاع لون الدقة
  Color _getAccuracyColor() {
    if (_accuracy >= 0.8) return Colors.green;
    if (_accuracy >= 0.6) return Colors.orange;
    return Colors.red;
  }

  /// أسترجاع لون الدقة للمراجعة
  Color _getReviewAccuracyColor(int reviewedSentences,
      {double accuracy = 0.0}) {
    if (reviewedSentences == 0) return Colors.grey;
    if (accuracy >= 0.8) return Colors.green;
    if (accuracy >= 0.6) return Colors.orange;
    return Colors.red;
  }

  /// تحميل مجموعات المراجعة
  Future<void> _loadGroups() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        setState(() {
          _isLoading = false;
          _groupsToReview.clear();
        });
        return;
      }

      final levelProvider = Provider.of<LevelProvider>(context, listen: false);
      final level = levelProvider.getLevelById(widget.levelId);

      if (level == null) {
        setState(() {
          _isLoading = false;
          _groupsToReview.clear();
        });
        return;
      }

      // البحث عن الدورة المحددة فقط
      final targetCycle = level.cycles.firstWhere(
        (cycle) => cycle.id == widget.cycleId,
        orElse: () => throw Exception('الدورة غير موجودة'),
      );

      debugPrint('جلب مجموعات المراجعة للدورة ${targetCycle.id}');
      _groupsToReview.clear();

      // جلب جميع المجموعات الفرعية في هذه الدورة
      for (final group in targetCycle.lessonGroups) {
        List<String> sentenceIds = [];

        // فحص نوع المجموعة وجلب البيانات من المسار المناسب
        if (group.type == LessonType.conversation) {
          // للمحادثات: جلب من readConversations
          final readConversationsDoc = await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .collection('readConversations')
              .doc(group.id.toString())
              .get();

          if (readConversationsDoc.exists) {
            final data = readConversationsDoc.data();
            if (data != null && data['sentences'] != null) {
              final List sentences = data['sentences'];
              sentenceIds = sentences
                  .map<String>((e) => e['sentenceId'] as String)
                  .toList();
              debugPrint(
                  'تم العثور على ${sentenceIds.length} جملة محادثة في المجموعة ${group.id}');
            }
          }
        } else {
          // للجمل العادية: جلب من readSentences
          final readSentencesSnapshot = await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .collection('readSentences')
              .where('levelId', isEqualTo: widget.levelId)
              .where('cycleId', isEqualTo: targetCycle.id)
              .where('groupId', isEqualTo: group.id)
              .get();

          sentenceIds =
              readSentencesSnapshot.docs.map((doc) => doc.id).toList();
          debugPrint(
              'تم العثور على ${sentenceIds.length} جملة عادية في المجموعة ${group.id}');
        }

        // إضافة المجموعة حتى لو كانت فارغة لعرض جميع المجموعات الفرعية
        _groupsToReview.add({
          'cycle': targetCycle,
          'group': group,
          'sentenceIds': sentenceIds,
        });
      }

      debugPrint('تم جلب ${_groupsToReview.length} مجموعة فرعية للمراجعة');

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل المجموعات: $e');
      setState(() {
        _isLoading = false;
        _groupsToReview.clear();
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _initTts();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.showGroups) {
        _loadGroups();
      } else {
        _loadReviewSentences();
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _flutterTts.stop();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.showGroups
            ? 'مراجعة المجموعات'
            : 'مراجعة ${widget.title} - المستوى ${widget.levelId}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: widget.showGroups ? _loadGroups : _loadReviewSentences,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Center(
        child: _isLoading
            ? const CircularProgressIndicator()
            : widget.showGroups
                ? _buildGroupsList()
                : _buildReviewContent(),
      ),
    );
  }

  /// تهيئة محاكاة القراءة
  Future<void> _initTts() async {
    await _flutterTts.setLanguage("en-US");
    await _flutterTts.setSpeechRate(0.5);
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setPitch(1.0);

    _flutterTts.setStartHandler(() {
      setState(() {
        _isSpeaking = true;
      });
    });

    _flutterTts.setCompletionHandler(() {
      setState(() {
        _isSpeaking = false;
      });
    });

    _flutterTts.setErrorHandler((error) {
      setState(() {
        _isSpeaking = false;
      });
      debugPrint('خطأ في محاكاة القراءة: $error');
    });
  }

  /// تحميل جمل المحادثة للمراجعة من المسار الجديد
  Future<void> _loadConversationSentences() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        setState(() {
          _isLoading = false;
          _reviewSentences = [];
        });
        return;
      }

      debugPrint('جلب محادثات المراجعة للمجموعة: ${widget.groupId}');
      debugPrint(
          'المسار: /users/${user.uid}/readConversations/${widget.groupId}');

      // جلب المحادثات المقروءة من المسار الجديد readConversations
      final readConversationsDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('readConversations')
          .doc(widget.groupId.toString())
          .get();

      if (!readConversationsDoc.exists) {
        debugPrint('لا توجد محادثات مقروءة في المجموعة ${widget.groupId}');
        setState(() {
          _isLoading = false;
          _reviewSentences = [];
        });
        return;
      }

      final data = readConversationsDoc.data();
      if (data == null || data['sentences'] == null) {
        debugPrint('لا توجد جمل محادثة محفوظة');
        setState(() {
          _isLoading = false;
          _reviewSentences = [];
        });
        return;
      }

      final List sentences = data['sentences'];
      debugPrint('تم العثور على ${sentences.length} جملة محادثة مقروءة');

      // ترتيب الجمل حسب الترتيب
      sentences.sort((a, b) {
        final orderA = a['order'] != null ? (a['order'] as num).toInt() : 0;
        final orderB = b['order'] != null ? (b['order'] as num).toInt() : 0;
        return orderA.compareTo(orderB);
      });

      _reviewSentences = [];

      // جلب تفاصيل الجمل من مجموعة sentences
      final sentenceIds =
          sentences.map<String>((e) => e['sentenceId'] as String).toList();

      debugPrint('معرفات الجمل المحفوظة: $sentenceIds');

      if (sentenceIds.isNotEmpty) {
        debugPrint('جلب تفاصيل ${sentenceIds.length} جملة محادثة');

        // جلب الجمل بدفعات من 10
        for (int i = 0; i < sentenceIds.length; i += 10) {
          final end =
              (i + 10 < sentenceIds.length) ? i + 10 : sentenceIds.length;
          final batch = sentenceIds.sublist(i, end);

          try {
            debugPrint('جلب دفعة من ${batch.length} جملة: $batch');

            // محاولة جلب من sentences أولاً
            var sentencesSnapshot = await FirebaseFirestore.instance
                .collection('sentences')
                .where(FieldPath.documentId, whereIn: batch)
                .get();

            debugPrint(
                'تم جلب ${sentencesSnapshot.docs.length} جملة من مجموعة sentences');

            // إذا لم توجد جمل في sentences، جرب conversations
            if (sentencesSnapshot.docs.isEmpty) {
              debugPrint(
                  'لم توجد جمل في sentences، جاري البحث في conversations...');

              final conversationsSnapshot = await FirebaseFirestore.instance
                  .collection('conversations')
                  .where(FieldPath.documentId, whereIn: batch)
                  .get();

              debugPrint(
                  'تم جلب ${conversationsSnapshot.docs.length} محادثة من مجموعة conversations');

              // إذا وجدت محادثات، استخرج الجمل منها
              if (conversationsSnapshot.docs.isNotEmpty) {
                for (final conversationDoc in conversationsSnapshot.docs) {
                  final conversationData = conversationDoc.data();
                  final List<dynamic> conversationSentences =
                      conversationData['sentences'] ?? [];

                  debugPrint(
                      'محادثة ${conversationDoc.id} تحتوي على ${conversationSentences.length} جملة');

                  for (int i = 0; i < conversationSentences.length; i++) {
                    final sentenceData = conversationSentences[i];

                    _reviewSentences.add(SentenceModel(
                      id: '${conversationDoc.id}_$i',
                      arabicText: sentenceData['arabicText'] ?? '',
                      englishText: sentenceData['englishText'] ?? '',
                      category: 'محادثة',
                      createdAt: (conversationData['createdAt'] as Timestamp?)
                              ?.toDate() ??
                          DateTime.now(),
                      readBy: {},
                      isFavorite: false,
                      audioUrl: sentenceData['audioUrl'],
                      difficulty: conversationData['difficulty'] ?? 'medium',
                      isReadByCurrentUser: true,
                      isFavoriteByCurrentUser: false,
                      isConversation: true,
                    ));
                  }
                }
              } else {
                // محاولة جلب المحادثة من مجموعة conversations باستخدام معرف المحادثة
                debugPrint('محاولة جلب المحادثة من conversations...');

                // البحث عن المحادثة المرتبطة بهذه المجموعة
                debugPrint(
                    'البحث عن محادثة للمجموعة ${widget.groupId} في المستوى ${widget.levelId} والدورة ${widget.cycleId}');

                // البحث عن محادثة بناءً على معايير المجموعة
                final conversationsQuery = await FirebaseFirestore.instance
                    .collection('conversations')
                    .where('level', isEqualTo: widget.levelId.toString())
                    .get();

                String? conversationId;

                // البحث في المحادثات الموجودة
                for (final conversationDoc in conversationsQuery.docs) {
                  final conversationData = conversationDoc.data();

                  // تحقق من أن هذه المحادثة تنتمي للمجموعة الصحيحة
                  // يمكن استخدام عنوان المحادثة أو معايير أخرى
                  final title = conversationData['title'] ?? '';

                  // إذا كان عنوان المحادثة يحتوي على رقم المجموعة أو معايير أخرى
                  if (title.contains('محادثة') ||
                      conversationDoc.id.contains(widget.groupId.toString())) {
                    conversationId = conversationDoc.id;
                    debugPrint(
                        'تم العثور على محادثة محتملة: $conversationId (عنوان: $title)');
                    break;
                  }
                }

                // إذا لم يتم العثور على محادثة، جرب استخدام أول محادثة متاحة
                if (conversationId == null &&
                    conversationsQuery.docs.isNotEmpty) {
                  conversationId = conversationsQuery.docs.first.id;
                  debugPrint('استخدام أول محادثة متاحة: $conversationId');
                }

                if (conversationId != null) {
                  try {
                    debugPrint('جلب المحادثة بالمعرف: $conversationId');

                    final conversationDoc = await FirebaseFirestore.instance
                        .collection('conversations')
                        .doc(conversationId)
                        .get();

                    if (conversationDoc.exists) {
                      final conversationData = conversationDoc.data()!;
                      final List<dynamic> messages =
                          conversationData['messages'] ?? [];

                      debugPrint(
                          'تم جلب محادثة تحتوي على ${messages.length} رسالة');

                      // تحويل رسائل المحادثة إلى SentenceModel
                      for (int i = 0; i < messages.length; i++) {
                        final messageData = messages[i];

                        _reviewSentences.add(SentenceModel(
                          id: '${conversationDoc.id}_message_$i',
                          arabicText: messageData['arabicText'] ?? '',
                          englishText: messageData['englishText'] ?? '',
                          category: conversationData['category'] ?? 'محادثة',
                          createdAt:
                              (conversationData['createdAt'] as Timestamp?)
                                      ?.toDate() ??
                                  DateTime.now(),
                          readBy: {},
                          isFavorite: false,
                          audioUrl: messageData['audioUrl'],
                          difficulty:
                              conversationData['difficulty'] ?? 'medium',
                          isReadByCurrentUser: true,
                          isFavoriteByCurrentUser: false,
                          isConversation: true,
                        ));
                      }

                      debugPrint(
                          'تم تحويل ${messages.length} رسالة إلى جمل للمراجعة');
                    } else {
                      debugPrint('لم توجد محادثة بالمعرف: $conversationId');

                      // إنشاء جمل وهمية بناءً على عدد الجمل المحفوظة
                      for (int i = 0; i < sentences.length; i++) {
                        final sentenceData = sentences[i];

                        _reviewSentences.add(SentenceModel(
                          id: sentenceData['sentenceId'] as String,
                          arabicText:
                              'جملة محادثة ${i + 1} (معرف: ${conversationId.substring(0, 8)})',
                          englishText:
                              'Conversation sentence ${i + 1} (ID: ${conversationId.substring(0, 8)})',
                          category: 'محادثة',
                          createdAt: DateTime.now(),
                          readBy: {},
                          isFavorite: false,
                          audioUrl: null,
                          difficulty: 'medium',
                          isReadByCurrentUser: true,
                          isFavoriteByCurrentUser: false,
                          isConversation: true,
                        ));
                      }
                    }
                  } catch (e) {
                    debugPrint('خطأ في جلب المحادثة $conversationId: $e');
                  }
                }

                debugPrint(
                    'تم جلب ${_reviewSentences.length} جملة محادثة للمراجعة');
              }
            } else {
              // معالجة الجمل من sentences كما هو معتاد
              debugPrint(
                  'معرفات الجمل المجلبة: ${sentencesSnapshot.docs.map((d) => d.id).toList()}');

              for (final doc in sentencesSnapshot.docs) {
                final sentenceData = doc.data();
                debugPrint('تم جلب جملة المحادثة: ${doc.id}');

                _reviewSentences.add(SentenceModel(
                  id: doc.id,
                  arabicText: sentenceData['arabicText'] ?? '',
                  englishText: sentenceData['englishText'] ?? '',
                  category: sentenceData['category'] ?? 'محادثة',
                  createdAt:
                      (sentenceData['createdAt'] as Timestamp?)?.toDate() ??
                          DateTime.now(),
                  readBy: {},
                  isFavorite: false,
                  audioUrl: sentenceData['audioUrl'],
                  difficulty: sentenceData['difficulty'] ?? 'medium',
                  isReadByCurrentUser: true,
                  isFavoriteByCurrentUser: false,
                  isConversation: true,
                ));
              }
            }
          } catch (e) {
            debugPrint('خطأ في جلب دفعة الجمل: $e');
          }
        }
      }

      debugPrint('تم جلب ${_reviewSentences.length} جملة محادثة للمراجعة');

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل محادثات المراجعة: $e');
      setState(() {
        _isLoading = false;
        _reviewSentences = [];
      });
    }
  }

  /// تحميل الجمل للمراجعة
  Future<void> _loadReviewSentences() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final levelProvider = Provider.of<LevelProvider>(context, listen: false);
      final level = levelProvider.getLevelById(widget.levelId);
      if (level != null) {
        try {
          final cycle = level.cycles.firstWhere(
            (cycle) => cycle.id == widget.cycleId,
            orElse: () => throw Exception('الدورة غير موجودة'),
          );
          final group = cycle.lessonGroups.firstWhere(
            (group) => group.id == widget.groupId,
            orElse: () => throw Exception('مجموعة الدروس غير موجودة'),
          );
          setState(() {
            _completedSentences = group.completedSentences;
            _accuracy = group.accuracy;
            _totalSentences = group.totalSentences;
          });

          // إذا كانت المجموعة من نوع محادثة conversation
          if (group.type == LessonType.conversation) {
            await _loadConversationSentences();
            return;
          }
        } catch (cycleError) {
          debugPrint('خطأ في العثور على الدورة أو المجموعة: $cycleError');
        }
      }

      // الحصول على المستخدم الحالي
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        setState(() {
          _isLoading = false;
          _reviewSentences = [];
        });
        return;
      }

      // طباعة معلومات البحث
      debugPrint(
          'البحث عن الجمل المقروءة للمستوى: ${widget.levelId}, الدورة: ${widget.cycleId}, المجموعة: ${widget.groupId}');

      // جلب الجمل المقروءة من Firebase للمجموعة المحددة
      final readSentencesQuery = FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('readSentences');

      // محاولة استخدام استعلام مباشر للحصول على الجمل المقروءة في المجموعة المحددة
      List<QueryDocumentSnapshot<Map<String, dynamic>>> filteredDocs = [];

      try {
        debugPrint(
            'محاولة استخدام استعلام مباشر للحصول على الجمل المقروءة في المجموعة');

        // نجلب الجمل المقروءة في المجموعة المحددة
        final directQuerySnapshot = await readSentencesQuery
            .where('levelId', isEqualTo: widget.levelId)
            .where('cycleId', isEqualTo: widget.cycleId)
            .where('groupId', isEqualTo: widget.groupId)
            .get();

        if (directQuerySnapshot.docs.isNotEmpty) {
          debugPrint(
              'تم العثور على ${directQuerySnapshot.docs.length} جملة في المجموعة باستخدام الاستعلام المباشر');
          filteredDocs = directQuerySnapshot.docs;
        } else {
          debugPrint(
              'لم يتم العثور على جمل في المجموعة باستخدام الاستعلام المباشر، جاري استخدام الطريقة البديلة');

          // إذا فشل الاستعلام المباشر، نستخدم الطريقة البديلة
          final allReadSentencesSnapshot = await readSentencesQuery.get();

          // تصفية الجمل يدويًا حسب المستوى والدورة والمجموعة
          filteredDocs = allReadSentencesSnapshot.docs.where((doc) {
            final data = doc.data();

            // التعامل مع القيم العددية أو النصية
            int? levelId;
            int? cycleId;
            int? groupId;

            // محاولة تحويل البيانات إلى أرقام
            if (data.containsKey('levelId')) {
              if (data['levelId'] is int) {
                levelId = data['levelId'] as int;
              } else if (data['levelId'] is String) {
                levelId = int.tryParse(data['levelId'] as String);
              } else if (data['levelId'] is double) {
                levelId = (data['levelId'] as double).toInt();
              }
            }

            if (data.containsKey('cycleId')) {
              if (data['cycleId'] is int) {
                cycleId = data['cycleId'] as int;
              } else if (data['cycleId'] is String) {
                cycleId = int.tryParse(data['cycleId'] as String);
              } else if (data['cycleId'] is double) {
                cycleId = (data['cycleId'] as double).toInt();
              }
            }

            if (data.containsKey('groupId')) {
              if (data['groupId'] is int) {
                groupId = data['groupId'] as int;
              } else if (data['groupId'] is String) {
                groupId = int.tryParse(data['groupId'] as String);
              } else if (data['groupId'] is double) {
                groupId = (data['groupId'] as double).toInt();
              }
            }

            // نبحث عن الجمل التي تنتمي للمجموعة المحددة
            return levelId == widget.levelId &&
                cycleId == widget.cycleId &&
                groupId == widget.groupId;
          }).toList();
        }
      } catch (e) {
        debugPrint('خطأ في استخدام الاستعلام المباشر: $e');

        // في حالة الخطأ، نستخدم الطريقة البديلة
        final allReadSentencesSnapshot = await readSentencesQuery.get();

        // تصفية الجمل يدويًا حسب المستوى والدورة والمجموعة
        filteredDocs = allReadSentencesSnapshot.docs.where((doc) {
          final data = doc.data();

          // التعامل مع القيم العددية أو النصية
          int? levelId;
          int? cycleId;
          int? groupId;

          // محاولة تحويل البيانات إلى أرقام
          if (data.containsKey('levelId')) {
            if (data['levelId'] is int) {
              levelId = data['levelId'] as int;
            } else if (data['levelId'] is String) {
              levelId = int.tryParse(data['levelId'] as String);
            } else if (data['levelId'] is double) {
              levelId = (data['levelId'] as double).toInt();
            }
          }

          if (data.containsKey('cycleId')) {
            if (data['cycleId'] is int) {
              cycleId = data['cycleId'] as int;
            } else if (data['cycleId'] is String) {
              cycleId = int.tryParse(data['cycleId'] as String);
            } else if (data['cycleId'] is double) {
              cycleId = (data['cycleId'] as double).toInt();
            }
          }

          if (data.containsKey('groupId')) {
            if (data['groupId'] is int) {
              groupId = data['groupId'] as int;
            } else if (data['groupId'] is String) {
              groupId = int.tryParse(data['groupId'] as String);
            } else if (data['groupId'] is double) {
              groupId = (data['groupId'] as double).toInt();
            }
          }

          // نبحث عن الجمل التي تنتمي للمجموعة المحددة
          return levelId == widget.levelId &&
              cycleId == widget.cycleId &&
              groupId == widget.groupId;
        }).toList();
      }

      // استخدام القائمة المصفاة
      debugPrint(
          'عدد الجمل المقروءة في الدورة بعد التصفية: ${filteredDocs.length}');

      // طباعة بعض المعلومات للتصحيح
      for (var i = 0; i < min(5, filteredDocs.length); i++) {
        final doc = filteredDocs[i];
        final data = doc.data();
        debugPrint('الجملة المصفاة ${i + 1}: ${doc.id}, البيانات: $data');
      }

      if (filteredDocs.isEmpty) {
        setState(() {
          _isLoading = false;
          _reviewSentences = [];
        });
        return;
      }

      // جلب تفاصيل الجمل من مجموعة sentences
      _reviewSentences = [];

      // تحقق من وجود محادثات
      List<String> conversationIds = [];
      List<String> regularSentenceIds = [];

      // فصل المحادثات عن الجمل العادية
      for (final doc in filteredDocs) {
        final data = doc.data();
        if (data.containsKey('isConversation') &&
            data['isConversation'] == true) {
          conversationIds.add(doc.id);
          debugPrint('تم العثور على محادثة للمراجعة: ${doc.id}');
        } else {
          regularSentenceIds.add(doc.id);
        }
      }

      // معالجة المحادثات إذا وجدت
      if (conversationIds.isNotEmpty) {
        debugPrint('عدد المحادثات للمراجعة: ${conversationIds.length}');

        // جلب المحادثات من Firestore
        for (int i = 0; i < conversationIds.length; i += 10) {
          final end = min(i + 10, conversationIds.length);
          final batch = conversationIds.sublist(i, end);

          if (batch.isNotEmpty) {
            try {
              final conversationsSnapshot = await FirebaseFirestore.instance
                  .collection('conversations')
                  .where(FieldPath.documentId, whereIn: batch)
                  .get();

              for (final doc in conversationsSnapshot.docs) {
                final data = doc.data();
                // إنشاء جملة وهمية لتمثيل المحادثة
                _reviewSentences.add(SentenceModel(
                  id: doc.id,
                  arabicText: 'محادثة: ${data['title'] ?? 'بدون عنوان'}',
                  englishText: 'Conversation: ${data['title'] ?? 'No title'}',
                  category: data['category'] ?? 'محادثة',
                  createdAt: (data['createdAt'] as Timestamp?)?.toDate() ??
                      DateTime.now(),
                  readBy: {},
                  isFavorite: false,
                  audioUrl: null,
                  difficulty: data['difficulty'] ?? 'medium',
                  isReadByCurrentUser: true, // المحادثة مقروءة بالفعل
                  isFavoriteByCurrentUser: false,
                  isConversation: true, // علامة لتمييز المحادثات
                ));

                debugPrint(
                    'تمت إضافة المحادثة للمراجعة: ${doc.id} - ${data['title']}');
              }
            } catch (e) {
              debugPrint('خطأ في جلب المحادثات: $e');
            }
          }
        }
      } else {
        debugPrint('لم يتم العثور على محادثات للمراجعة');
      }

      // معالجة الجمل العادية
      for (int i = 0; i < regularSentenceIds.length; i += 10) {
        final end = min(i + 10, regularSentenceIds.length);
        final batch = regularSentenceIds.sublist(i, end);

        if (batch.isNotEmpty) {
          final sentencesSnapshot = await FirebaseFirestore.instance
              .collection('sentences')
              .where(FieldPath.documentId, whereIn: batch)
              .get();

          for (final doc in sentencesSnapshot.docs) {
            final data = doc.data();
            _reviewSentences.add(SentenceModel(
              id: doc.id,
              arabicText: data['arabicText'] ?? '',
              englishText: data['englishText'] ?? '',
              category: data['category'] ?? 'عام',
              createdAt:
                  (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
              readBy: {},
              isFavorite: false,
              audioUrl: data['audioUrl'],
              difficulty: data['difficulty'],
              isReadByCurrentUser: true, // الجملة مقروءة بالفعل
              isFavoriteByCurrentUser: false,
              isConversation: false, // ليست محادثة
            ));
          }
        }
      }

      debugPrint('تم جلب ${_reviewSentences.length} جملة للمراجعة');

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل جمل المراجعة: $e');
      setState(() {
        _isLoading = false;
        _reviewSentences = [];
      });
    }
  }

  /// بناء معلومات التقدم
  Widget _buildProgressInfo() {
    return FutureBuilder<Map<String, dynamic>>(
      future: _fetchReviewStats(widget.levelId, widget.cycleId, widget.groupId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'خطأ في تحميل الإحصائيات: ${snapshot.error}',
              style: const TextStyle(color: Colors.red),
            ),
          );
        }

        final stats = snapshot.data ??
            {
              'completedSentences': _completedSentences,
              'accuracy': _accuracy,
              'totalSentences': _totalSentences,
            };

        return Container(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatCard(
                    'الجمل المكتملة',
                    '${stats['completedSentences']}/${stats['totalSentences']}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                  _buildStatCard(
                    'الدقة',
                    '${(stats['accuracy'] * 100).toStringAsFixed(1)}%',
                    Icons.analytics,
                    _getAccuracyColor(),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              if (widget.showGroups)
                Text(
                  'اختر مجموعة للمراجعة',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
            ],
          ),
        );
      },
    );
  }

  /// بناء بطاقة إحصائيات
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(title, style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة جملة المحادثة بتنسيق chat bubble
  Widget _buildConversationSentenceCard(SentenceModel sentence, int index) {
    // تحديد ما إذا كانت الجملة من الشخص A أو B (بناءً على الفهرس)
    final bool isPersonA = index % 2 == 0;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // عرض عداد الجملة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '${index + 1}/${_reviewSentences.length}',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Chat bubble
          Row(
            mainAxisAlignment:
                isPersonA ? MainAxisAlignment.end : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Avatar للشخص B (يسار)
              if (!isPersonA)
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.green[100],
                    child:
                        const Icon(Icons.person, size: 16, color: Colors.green),
                  ),
                ),

              // فقاعة المحادثة
              Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.7,
                ),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isPersonA ? Colors.blue[100] : Colors.grey[100],
                  borderRadius: BorderRadius.circular(16).copyWith(
                    bottomRight: isPersonA ? const Radius.circular(0) : null,
                    bottomLeft: !isPersonA ? const Radius.circular(0) : null,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: isPersonA
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.start,
                  children: [
                    // النص الإنجليزي
                    Text(
                      sentence.englishText,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.left,
                      textDirection: TextDirection.ltr,
                    ),
                    const SizedBox(height: 8),
                    // الترجمة العربية
                    Text(
                      sentence.arabicText,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.right,
                      textDirection: TextDirection.rtl,
                    ),
                  ],
                ),
              ),

              // Avatar للشخص A (يمين)
              if (isPersonA)
                Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.blue[100],
                    child:
                        const Icon(Icons.person, size: 16, color: Colors.blue),
                  ),
                ),
            ],
          ),

          // أزرار المراجعة للمحادثة
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // زر الاستماع
                ElevatedButton.icon(
                  onPressed: () => _speakSentence(sentence.englishText),
                  icon: Icon(
                    _isSpeaking ? Icons.stop : Icons.volume_up,
                    size: 18,
                  ),
                  label: Text(_isSpeaking ? 'إيقاف' : 'استماع'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),

                // زر الاختبار
                ElevatedButton.icon(
                  onPressed: () => _showQuizDialog(sentence),
                  icon: const Icon(Icons.quiz, size: 18),
                  label: const Text('اختبار'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // أزرار إضافية
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // زر تعليم كمقروءة
                OutlinedButton.icon(
                  onPressed: () => _markAsReviewed(sentence),
                  icon: const Icon(Icons.check_circle_outline),
                  label: const Text('مقروءة'),
                ),

                // زر إضافة للمفضلة
                OutlinedButton.icon(
                  onPressed: () => _addToFavorites(sentence),
                  icon: const Icon(Icons.favorite_border),
                  label: const Text('مفضلة'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الجملة
  Widget _buildSentenceCard(SentenceModel sentence, int index) {
    // إذا كانت جملة محادثة، استخدم تنسيق chat bubble
    if (sentence.isConversation) {
      return _buildConversationSentenceCard(sentence, index);
    }
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ListTile(
            title: Text(
              sentence.arabicText,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              children: [
                const SizedBox(height: 16),
                Text(
                  sentence.englishText,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 18),
                ),
                if (sentence.isConversation) ...[
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () => _openConversation(sentence),
                    icon: const Icon(Icons.chat),
                    label: const Text('عرض المحادثة كاملة'),
                  ),
                ],
              ],
            ),
          ),
          const Spacer(),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // زر الاستماع
                    ElevatedButton.icon(
                      onPressed: () => _speakSentence(sentence.englishText),
                      icon: Icon(
                        _isSpeaking ? Icons.stop : Icons.volume_up,
                        size: 18,
                      ),
                      label: Text(_isSpeaking ? 'إيقاف' : 'استماع'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),

                    // عداد الجمل
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '${index + 1}/${_reviewSentences.length}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    // زر الاختبار الحقيقي
                    ElevatedButton.icon(
                      onPressed: () => _showQuizDialog(sentence),
                      icon: const Icon(Icons.quiz, size: 18),
                      label: const Text('اختبار'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // أزرار إضافية للمراجعة
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // زر تعليم كمقروءة في المراجعة
                    OutlinedButton.icon(
                      onPressed: () => _markAsReviewed(sentence),
                      icon: const Icon(Icons.check_circle_outline),
                      label: const Text('مقروءة'),
                    ),

                    // زر إضافة للمفضلة
                    OutlinedButton.icon(
                      onPressed: () => _addToFavorites(sentence),
                      icon: const Icon(Icons.favorite_border),
                      label: const Text('مفضلة'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// قراءة الجملة صوتياً
  Future<void> _speakSentence(String text) async {
    if (_isSpeaking) {
      await _flutterTts.stop();
      setState(() => _isSpeaking = false);
    } else {
      await _flutterTts.speak(text);
    }
  }

  /// فتح نافذة الاختبار للمراجعة
  void _showQuizDialog(SentenceModel sentence) async {
    try {
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return QuizDialog(
            sentence: sentence,
          );
        },
      );

      if (result != null && result['success'] == true) {
        // تحديث الإحصائيات المحلية
        setState(() {
          _completedSentences++;
          _accuracy = _completedSentences / _reviewSentences.length;
        });

        // منح نقاط المراجعة
        await _awardReviewPoints(sentence, result);

        // حفظ التقدم
        await _saveReviewProgress();

        // عرض رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('ممتاز! تم إنجاز مراجعة الجملة بنجاح'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في فتح نافذة الاختبار: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ في فتح الاختبار'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// عرض تأثير النقاط
  void _showPointsAnimation(int points, PointType type) {
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        barrierColor: Colors.transparent,
        builder: (context) => PointsAnimation(
          points: points,
          type: type,
          onComplete: () {
            Navigator.of(context).pop();
          },
        ),
      );
    }
  }

  /// منح نقاط المراجعة
  Future<void> _awardReviewPoints(
      SentenceModel sentence, Map<String, dynamic> result) async {
    try {
      final pointsProvider =
          Provider.of<PointsProvider>(context, listen: false);

      // حساب النقاط حسب الأداء
      final double accuracy = result['accuracy'] ?? 0.0;
      int reviewPoints = 0;

      if (accuracy >= 0.9) {
        reviewPoints = 15; // نقاط ممتازة للمراجعة
      } else if (accuracy >= 0.8) {
        reviewPoints = 12; // نقاط جيدة
      } else if (accuracy >= 0.7) {
        reviewPoints = 8; // نقاط مقبولة
      } else {
        reviewPoints = 5; // نقاط أساسية
      }

      // إضافة نقاط المراجعة
      await pointsProvider.addPoints(
        reviewPoints,
        PointType.educational,
        'مراجعة جملة: ${sentence.arabicText.substring(0, 20)}...',
      );

      // عرض تأثير النقاط
      _showPointsAnimation(reviewPoints, PointType.educational);

      debugPrint(
          'تم منح $reviewPoints نقطة للمراجعة (دقة: ${(accuracy * 100).toStringAsFixed(1)}%)');
    } catch (e) {
      debugPrint('خطأ في منح نقاط المراجعة: $e');
    }
  }

  /// حفظ تقدم المراجعة
  Future<void> _saveReviewProgress() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // حفظ إحصائيات المراجعة
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('reviewStats')
          .doc('${widget.levelId}_${widget.cycleId}_${widget.groupId}')
          .set({
        'levelId': widget.levelId,
        'cycleId': widget.cycleId,
        'groupId': widget.groupId,
        'totalSentences': _reviewSentences.length,
        'completedSentences': _completedSentences,
        'accuracy': _accuracy,
        'lastReviewedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      // فحص إكمال المراجعة وتحديث تقدم المجموعة
      final isReviewCompleted = _completedSentences >= _reviewSentences.length;

      if (isReviewCompleted) {
        await _handleReviewCompletion();
      }

      debugPrint(
          'تم حفظ تقدم المراجعة: $_completedSentences/${_reviewSentences.length}');
    } catch (e) {
      debugPrint('خطأ في حفظ تقدم المراجعة: $e');
    }
  }

  /// معالجة إكمال المراجعة وفتح المجموعة التالية
  Future<void> _handleReviewCompletion() async {
    try {
      debugPrint('بدء معالجة إكمال المراجعة...');

      // الحصول على مزود المستويات
      final levelProvider = Provider.of<LevelProvider>(context, listen: false);

      // تحديث تقدم مجموعة المراجعة في LevelProvider
      await levelProvider.updateLessonGroupProgress(
        widget.levelId,
        widget.cycleId,
        widget.groupId,
        _reviewSentences.length, // جميع الجمل مكتملة
        _accuracy,
        true, // المجموعة مكتملة
      );

      debugPrint('تم تحديث تقدم مجموعة المراجعة');

      // منح نقاط إضافية لإكمال المراجعة
      if (mounted) {
        final pointsProvider =
            Provider.of<PointsProvider>(context, listen: false);
        await pointsProvider.addPoints(
          50, // نقاط إضافية لإكمال المراجعة
          PointType.educational,
          'إكمال مراجعة المجموعة ${widget.groupId} في المستوى ${widget.levelId}',
        );

        // عرض تأثير النقاط
        _showPointsAnimation(50, PointType.educational);
      }

      // عرض رسالة إكمال المراجعة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('تهانينا! تم إكمال مراجعة المجموعة بنجاح! (+50 نقطة)'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 4),
          ),
        );
      }

      // حذف جلسة المراجعة المكتملة
      await _sessionService.clearSession(
        levelId: widget.levelId,
        cycleId: widget.cycleId,
        groupId: widget.groupId,
      );

      debugPrint('تم إكمال معالجة إكمال المراجعة بنجاح');
    } catch (e) {
      debugPrint('خطأ في معالجة إكمال المراجعة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في حفظ تقدم المراجعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تعليم الجملة كمقروءة في المراجعة
  Future<void> _markAsReviewed(SentenceModel sentence) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // تحديث الإحصائيات المحلية
      setState(() {
        _completedSentences++;
        _accuracy = _completedSentences / _reviewSentences.length;
      });

      // حفظ في reviewStats
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('reviewStats')
          .doc('${widget.levelId}_${widget.cycleId}_${widget.groupId}')
          .set({
        'completedSentences': FieldValue.increment(1),
        'lastReviewedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      // حفظ التقدم العام
      await _saveReviewProgress();

      // منح نقاط المراجعة
      if (mounted) {
        final pointsProvider =
            Provider.of<PointsProvider>(context, listen: false);
        await pointsProvider.addPoints(
          3, // نقاط مراجعة بسيطة
          PointType.educational,
          'مراجعة جملة: ${sentence.arabicText.substring(0, 20)}...',
        );

        // عرض تأثير النقاط
        _showPointsAnimation(3, PointType.educational);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تعليم الجملة كمقروءة في المراجعة (+3 نقاط)'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في تعليم الجملة كمقروءة: $e');
    }
  }

  /// إضافة الجملة للمفضلة
  Future<void> _addToFavorites(SentenceModel sentence) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // حفظ في favorites
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('favorites')
          .doc(sentence.id)
          .set({
        'sentenceId': sentence.id,
        'arabicText': sentence.arabicText,
        'englishText': sentence.englishText,
        'category': sentence.category,
        'addedAt': FieldValue.serverTimestamp(),
        'fromReview': true,
      });

      // منح نقطة مكافأة
      if (mounted) {
        final pointsProvider =
            Provider.of<PointsProvider>(context, listen: false);
        await pointsProvider.addPoints(
          1,
          PointType.reward,
          'إضافة جملة للمفضلة',
        );

        // عرض تأثير النقاط
        _showPointsAnimation(1, PointType.reward);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة الجملة للمفضلة (+1 نقطة مكافأة)'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في إضافة الجملة للمفضلة: $e');
    }
  }

  /// فتح المحادثة
  void _openConversation(SentenceModel sentence) {
    if (sentence.isConversation) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ConversationDetailScreen(
            conversationId: sentence.id,
          ),
        ),
      );
    }
  }

  /// بناء محتوى المراجعة
  Widget _buildReviewContent() {
    if (_reviewSentences.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.info_outline, size: 48, color: Colors.blue),
            const SizedBox(height: 16),
            const Text(
              'لا توجد جمل للمراجعة',
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed: _loadReviewSentences,
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        _buildProgressInfo(),
        Expanded(
          child: PageView.builder(
            controller: _pageController,
            itemCount: _reviewSentences.length,
            itemBuilder: (context, index) {
              final sentence = _reviewSentences[index];
              return _buildSentenceCard(sentence, index);
            },
            onPageChanged: (index) {
              // يمكن إضافة منطق هنا إذا لزم الأمر
            },
          ),
        ),
      ],
    );
  }

  /// جلب إحصائيات المراجعة
  Future<Map<String, dynamic>> _fetchReviewStats(
    int levelId,
    int cycleId,
    int groupId,
  ) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final levelProvider = Provider.of<LevelProvider>(context, listen: false);
      final level = levelProvider.getLevelById(levelId);
      if (level == null) {
        throw Exception('المستوى غير موجود');
      }

      final cycle = level.cycles.firstWhere(
        (c) => c.id == cycleId,
        orElse: () => throw Exception('الدورة غير موجودة'),
      );

      final group = cycle.lessonGroups.firstWhere(
        (g) => g.id == groupId,
        orElse: () => throw Exception('المجموعة غير موجودة'),
      );

      return {
        'completedSentences': group.completedSentences,
        'accuracy': group.accuracy,
        'totalSentences': group.totalSentences,
      };
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات المراجعة: $e');
      return {
        'completedSentences': _completedSentences,
        'accuracy': _accuracy,
        'totalSentences': _totalSentences,
      };
    }
  }

  Widget _buildGroupsList() {
    if (_groupsToReview.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.search_off, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'لم يتم العثور على مجموعات للمراجعة',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadGroups,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return ListView(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Text(
                'مراجعة ${widget.title}',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                'المستوى ${widget.levelId} - الدورة ${widget.cycleId}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ],
          ),
        ),
        ..._groupsToReview.map((groupData) {
          final LessonGroup group = groupData['group'] as LessonGroup;
          final Cycle cycle = groupData['cycle'] as Cycle;
          final List<String> sentenceIds =
              groupData['sentenceIds'] as List<String>;

          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            elevation: 3,
            child: InkWell(
              onTap: sentenceIds.isNotEmpty
                  ? () {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ReviewScreen(
                            levelId: widget.levelId,
                            cycleId: cycle.id,
                            groupId: group.id,
                            title: group.title,
                            showGroups: false,
                          ),
                        ),
                      );
                    }
                  : null,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          group.type == LessonType.conversation
                              ? Icons.chat_bubble_outline
                              : Icons.format_list_bulleted,
                          color: sentenceIds.isNotEmpty
                              ? Colors.blue
                              : Colors.grey,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            group.title,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: sentenceIds.isNotEmpty
                                  ? Colors.black
                                  : Colors.grey,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: sentenceIds.isNotEmpty
                                ? Colors.blue[100]
                                : Colors.grey[200],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${sentenceIds.length} جملة',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: sentenceIds.isNotEmpty
                                  ? Colors.blue[800]
                                  : Colors.grey[600],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    FutureBuilder<Map<String, dynamic>>(
                      future:
                          _fetchReviewStats(widget.levelId, cycle.id, group.id),
                      builder: (context, snapshot) {
                        int reviewedSentences = 0;
                        double reviewAccuracy = 0.85;

                        if (snapshot.hasData && snapshot.data != null) {
                          final data = snapshot.data!;
                          // استخدام completedSentences بدلاً من reviewedSentences
                          reviewedSentences = data['completedSentences'] != null
                              ? (data['completedSentences'] as num).toInt()
                              : 0;
                          reviewAccuracy = data['accuracy'] != null
                              ? (data['accuracy'] as num).toDouble()
                              : 0.85;
                        }

                        return Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text('جمل تمت مراجعتها'),
                                Text(
                                  '$reviewedSentences',
                                  style: TextStyle(
                                    color: _getReviewAccuracyColor(
                                      reviewedSentences,
                                      accuracy: reviewAccuracy,
                                    ),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            LinearProgressIndicator(
                              value: reviewedSentences > 0 ? reviewAccuracy : 0,
                              backgroundColor: Colors.grey.shade200,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _getReviewAccuracyColor(
                                  reviewedSentences,
                                  accuracy: reviewAccuracy,
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ],
    );
  }
}
