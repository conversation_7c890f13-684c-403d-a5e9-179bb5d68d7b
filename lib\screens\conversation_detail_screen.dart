import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/conversation_model.dart';
import '../models/sentence_model.dart';
import '../services/conversation_service.dart';
import '../services/conversation_session_service.dart';
import '../theme/app_theme.dart';
import '../widgets/quiz_dialog.dart';
import '../widgets/chat_bubble_new2.dart';

class ConversationDetailScreen extends StatefulWidget {
  final String conversationId;
  final int? levelId;
  final int? cycleId;
  final int? groupId;

  const ConversationDetailScreen({
    Key? key,
    required this.conversationId,
    this.levelId,
    this.cycleId,
    this.groupId,
  }) : super(key: key);

  @override
  State<ConversationDetailScreen> createState() =>
      _ConversationDetailScreenState();
}

class _ConversationDetailScreenState extends State<ConversationDetailScreen> {
  int _visibleMessageCount = 1; // Inicialmente solo mostrar el primer mensaje
  ConversationModel? _conversation;
  bool _isLoading = true;
  String? _error;
  final ConversationSessionService _sessionService =
      ConversationSessionService();

  @override
  void initState() {
    super.initState();
    _loadConversation();
  }

  Future<void> _loadConversation() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final conversationService =
          Provider.of<ConversationService>(context, listen: false);
      final conversation =
          await conversationService.getConversation(widget.conversationId);

      if (conversation == null) {
        setState(() {
          _error = 'لم يتم العثور على المحادثة';
          _isLoading = false;
        });
        return;
      }

      // استعادة الجلسة المحفوظة إذا كانت جزءًا من مسار التعلم
      if (widget.levelId != null &&
          widget.cycleId != null &&
          widget.groupId != null) {
        final savedSession = await _sessionService.getConversationSession(
          levelId: widget.levelId!,
          cycleId: widget.cycleId!,
          groupId: widget.groupId!,
        );

        if (savedSession != null &&
            savedSession['conversationId'] == widget.conversationId) {
          final displayedMessageIds =
              savedSession['displayedMessageIds'] as List<String>;
          _visibleMessageCount = displayedMessageIds.length;
          debugPrint(
              'تم استعادة جلسة المحادثة: عدد الرسائل المعروضة $_visibleMessageCount');
        }
      }

      setState(() {
        _conversation = conversation;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ: $e';
        _isLoading = false;
      });
    }
  }

  void _markMessageAsRead(int index) async {
    if (_conversation == null || index >= _conversation!.messages.length) {
      return;
    }

    try {
      final conversationService =
          Provider.of<ConversationService>(context, listen: false);
      await conversationService.updateReadProgress(
        widget.conversationId,
        index,
        0.0, // No hay puntuación de pronunciación en este caso
        levelId: widget.levelId,
        cycleId: widget.cycleId,
        groupId: widget.groupId,
      );

      // Actualizar el estado local sin recargar toda la conversación
      if (mounted) {
        setState(() {
          // Marcar el mensaje como leído localmente
          final userId = conversationService.currentUserId;
          if (userId != null) {
            // Actualizar el modelo local
            final updatedMessage =
                _conversation!.messages[index].markAsRead(userId);
            // Reemplazar el mensaje en la lista
            _conversation!.messages[index] = updatedMessage;

            // Mostrar el siguiente mensaje si no es el último
            if (index < _conversation!.messages.length - 1) {
              _visibleMessageCount =
                  index + 2; // Mostrar hasta el siguiente mensaje
            }
          }
        });

        // حفظ الجلسة المحدثة إذا كانت جزءًا من مسار التعلم
        if (widget.levelId != null &&
            widget.cycleId != null &&
            widget.groupId != null) {
          _sessionService.saveConversationSession(
            levelId: widget.levelId!,
            cycleId: widget.cycleId!,
            groupId: widget.groupId!,
            conversation: _conversation!,
            visibleMessageCount: _visibleMessageCount,
          );
        }
      }

      // Registrar información de depuración
      if (widget.levelId != null &&
          widget.cycleId != null &&
          widget.groupId != null) {
        debugPrint(
            'Mensaje marcado como leído con información de nivel/ciclo/grupo');
        debugPrint(
            'levelId: ${widget.levelId}, cycleId: ${widget.cycleId}, groupId: ${widget.groupId}');
      } else {
        debugPrint(
            'Mensaje marcado como leído sin información de nivel/ciclo/grupo');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    }
  }

  void _openQuizDialog(int index) {
    if (_conversation == null || index >= _conversation!.messages.length) {
      return;
    }

    final message = _conversation!.messages[index];

    // Convertir MessageModel a SentenceModel para usar con QuizDialog
    final sentence = SentenceModel(
      id: message.id,
      arabicText: message.arabicText,
      englishText: message.englishText,
      category: _conversation!.category,
      createdAt: message.createdAt,
      readBy: {},
      isFavorite: false,
      difficulty: _conversation!.difficulty,
      isReadByCurrentUser: false,
      isFavoriteByCurrentUser: false,
    );

    // Guardar el contexto actual antes de la operación asíncrona
    final BuildContext currentContext = context;

    // Mostrar el diálogo de quiz y obtener el resultado
    showDialog(
      context: currentContext,
      builder: (context) => QuizDialog(
        sentence: sentence,
      ),
    ).then((result) async {
      // Después de cerrar el diálogo, actualizar el progreso
      try {
        if (!mounted) return;

        // Obtener el servicio antes de la operación asíncrona
        final conversationService =
            Provider.of<ConversationService>(context, listen: false);

        // Obtener la puntuación real del quiz (si está disponible)
        // Si no hay resultado, usar un valor predeterminado
        double score = 0.0;
        if (result != null && result is Map<String, dynamic>) {
          score = result['score'] ?? 0.0;
        }

        // Actualizar en la base de datos
        await conversationService.updateMessageTestResults(
          widget.conversationId,
          message.id,
          score,
        );

        // Actualizar el estado local sin recargar toda la conversación
        if (mounted) {
          setState(() {
            // Actualizar la puntuación localmente
            final userId = conversationService.currentUserId;
            if (userId != null) {
              // Actualizar el modelo local
              final updatedMessage = _conversation!.messages[index]
                  .updateTestResults(userId, score);
              // Reemplazar el mensaje en la lista
              _conversation!.messages[index] = updatedMessage;

              // Actualizar también la puntuación de pronunciación global
              _conversation = _conversation!.updatePronunciationScore(userId);
            }
          });
        }
      } catch (e) {
        if (mounted) {
          // Usar context actual en lugar de currentContext
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ: $e')),
          );
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_conversation?.title ?? 'محادثة'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text(_error!))
              : _buildConversationContent(),
    );
  }

  Widget _buildConversationContent() {
    if (_conversation == null) {
      return const Center(child: Text('لا توجد بيانات'));
    }

    return Column(
      children: [
        // Área de la conversación (75% de la pantalla)
        Expanded(
          flex: 75, // 75% de la pantalla
          child: Card(
            margin: const EdgeInsets.all(8),
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Título de la tarjeta
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'المحادثة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        // Botón para mostrar todas las conversaciones
                        if (_visibleMessageCount <
                            _conversation!.messages.length)
                          TextButton.icon(
                            icon: const Icon(Icons.visibility, size: 16),
                            label: const Text('عرض الكل'),
                            onPressed: () {
                              setState(() {
                                _visibleMessageCount =
                                    _conversation!.messages.length;
                              });

                              // حفظ الجلسة المحدثة
                              if (widget.levelId != null &&
                                  widget.cycleId != null &&
                                  widget.groupId != null) {
                                _sessionService.saveConversationSession(
                                  levelId: widget.levelId!,
                                  cycleId: widget.cycleId!,
                                  groupId: widget.groupId!,
                                  conversation: _conversation!,
                                  visibleMessageCount: _visibleMessageCount,
                                );
                              }
                            },
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Lista de mensajes
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      itemCount: _visibleMessageCount,
                      itemBuilder: (context, index) {
                        if (index >= _conversation!.messages.length) {
                          return const SizedBox.shrink();
                        }
                        return _buildMessageBubble(index);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Área de estadísticas (25% de la pantalla)
        Expanded(
          flex: 25, // 25% de la pantalla
          child: Card(
            margin: const EdgeInsets.fromLTRB(8, 0, 8, 8),
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: _buildStatsPanel(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMessageBubble(int index) {
    final message = _conversation!.messages[index];
    final userId =
        Provider.of<ConversationService>(context, listen: false).currentUserId;
    final bool isRead = userId != null && message.isReadByUser(userId);
    final double testScore =
        userId != null ? message.getTestScore(userId) : 0.0;

    // Determinar si este mensaje es el activo (el siguiente no leído)
    bool isActive = false;
    if (!isRead) {
      // Si este mensaje no está leído, verificar si es el primero no leído
      bool isPreviousRead = true;
      if (index > 0) {
        // Verificar si el mensaje anterior está leído
        final previousMessage = _conversation!.messages[index - 1];
        isPreviousRead = userId != null && previousMessage.isReadByUser(userId);
      }
      // Este mensaje es activo si es el primero no leído
      isActive = isPreviousRead;
    }

    return ChatBubble(
      message: message,
      isRead: isRead,
      testScore: testScore,
      onMarkAsRead: _markMessageAsRead,
      onOpenQuiz: _openQuizDialog,
      index: index,
      isActive: isActive,
    );
  }

  Widget _buildStatsPanel() {
    if (_conversation == null) return const SizedBox.shrink();

    final userId =
        Provider.of<ConversationService>(context, listen: false).currentUserId;
    if (userId == null) {
      return const Center(child: Text('يرجى تسجيل الدخول لعرض الإحصائيات'));
    }

    // Calcular estadísticas
    final int totalMessages = _conversation!.messages.length;
    final double progress = _conversation!.getReadProgress(userId);
    final int readCount = (progress * totalMessages).round();
    final double pronunciationScore =
        _conversation!.getPronunciationScore(userId);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات المحادثة',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),

        // Progreso de lectura
        Row(
          children: [
            const Text(
              'التقدم:',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  LinearProgressIndicator(
                    value: progress,
                    backgroundColor: Colors.grey.shade300,
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      AppTheme.primaryColor,
                    ),
                    minHeight: 6,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    '$readCount من $totalMessages رسائل (${(progress * 100).toInt()}%)',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Puntuación de pronunciación
        Row(
          children: [
            const Text(
              'النطق:',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  LinearProgressIndicator(
                    value: pronunciationScore,
                    backgroundColor: Colors.grey.shade300,
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Colors.orange,
                    ),
                    minHeight: 6,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    '${(pronunciationScore * 100).toInt()}%',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
