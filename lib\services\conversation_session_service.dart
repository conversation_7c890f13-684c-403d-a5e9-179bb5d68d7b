import 'package:flutter/foundation.dart';
import '../models/conversation_model.dart';
import 'learning_path_session_service.dart';

/// خدمة إدارة جلسات المحادثات في مسار التعلم
class ConversationSessionService {
  final LearningPathSessionService _sessionService = LearningPathSessionService();

  /// حفظ جلسة محادثة مع الرسائل المعروضة حاليًا
  Future<void> saveConversationSession({
    required int levelId,
    required int cycleId,
    required int groupId,
    required ConversationModel conversation,
    required int visibleMessageCount,
  }) async {
    try {
      // إنشاء قائمة بمعرفات الرسائل المعروضة
      final displayedMessageIds = conversation.messages
          .take(visibleMessageCount)
          .map((message) => message.id)
          .toList();

      await _sessionService.saveConversationSession(
        levelId: levelId,
        cycleId: cycleId,
        groupId: groupId,
        conversationId: conversation.id,
        displayedMessageIds: displayedMessageIds,
        currentMessageIndex: visibleMessageCount - 1,
      );

      debugPrint('تم حفظ جلسة المحادثة: ${conversation.title}');
      debugPrint('عدد الرسائل المعروضة: $visibleMessageCount');
    } catch (e) {
      debugPrint('خطأ في حفظ جلسة المحادثة: $e');
    }
  }

  /// استعادة جلسة محادثة
  Future<Map<String, dynamic>?> getConversationSession({
    required int levelId,
    required int cycleId,
    required int groupId,
  }) async {
    try {
      final session = await _sessionService.getConversationSession(
        levelId: levelId,
        cycleId: cycleId,
        groupId: groupId,
      );

      if (session != null) {
        debugPrint('تم استعادة جلسة المحادثة: ${session['conversationId']}');
        debugPrint('عدد الرسائل المعروضة: ${session['displayedMessageIds'].length}');
      }

      return session;
    } catch (e) {
      debugPrint('خطأ في استعادة جلسة المحادثة: $e');
      return null;
    }
  }

  /// تحديث عدد الرسائل المعروضة في الجلسة
  Future<void> updateVisibleMessageCount({
    required int levelId,
    required int cycleId,
    required int groupId,
    required int newCount,
  }) async {
    try {
      await _sessionService.updateSessionPosition(
        levelId: levelId,
        cycleId: cycleId,
        groupId: groupId,
        newPosition: newCount,
      );

      debugPrint('تم تحديث عدد الرسائل المعروضة إلى: $newCount');
    } catch (e) {
      debugPrint('خطأ في تحديث عدد الرسائل المعروضة: $e');
    }
  }

  /// حذف جلسة محادثة مكتملة
  Future<void> clearConversationSession({
    required int levelId,
    required int cycleId,
    required int groupId,
  }) async {
    try {
      await _sessionService.clearSession(
        levelId: levelId,
        cycleId: cycleId,
        groupId: groupId,
      );

      debugPrint('تم حذف جلسة المحادثة');
    } catch (e) {
      debugPrint('خطأ في حذف جلسة المحادثة: $e');
    }
  }

  /// التحقق من وجود جلسة محادثة نشطة
  Future<bool> hasActiveConversationSession({
    required int levelId,
    required int cycleId,
    required int groupId,
  }) async {
    try {
      return await _sessionService.hasActiveSession(
        levelId: levelId,
        cycleId: cycleId,
        groupId: groupId,
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من جلسة المحادثة النشطة: $e');
      return false;
    }
  }
}
