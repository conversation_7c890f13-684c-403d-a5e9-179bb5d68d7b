# نظام إدارة جلسات مسار التعلم

## 📋 نظرة عامة

تم تطوير نظام شامل لإدارة جلسات مسار التعلم لحل مشكلة فقدان حالة الجمل والمحادثات عند الخروج والعودة للتطبيق. النظام يحفظ تقدم المستخدم ويستعيده بدقة.

## 🎯 المشاكل التي تم حلها

### المشكلة الأصلية:
- عند دخول المستخدم لدفعة أو محادثة في مسار التعلم
- ثم الخروج والعودة
- كانت الجمل تتغير أو المحادثة تصبح فارغة
- المستخدم يفقد تقدمه ويحتاج لبدء من جديد

### الحل المطبق:
✅ **حفظ حالة الجلسة**: يتم حفظ الجمل/الرسائل المعروضة حاليًا  
✅ **استعادة التقدم**: عند العودة، يتم استكمال من نفس النقطة  
✅ **إزالة الجمل المقروءة**: الجمل المقروءة تختفي تلقائيًا  
✅ **تنظيف الجلسات**: حذف الجلسات المكتملة تلقائيًا  

## 🏗️ هيكل النظام

### 1. خدمة إدارة الجلسات الأساسية
**الملف**: `lib/services/learning_path_session_service.dart`

```dart
class LearningPathSessionService {
  // حفظ جلسة دفعة جمل
  Future<void> saveBatchSession({...});
  
  // استعادة جلسة دفعة جمل
  Future<Map<String, dynamic>?> getBatchSession({...});
  
  // حفظ جلسة محادثة
  Future<void> saveConversationSession({...});
  
  // استعادة جلسة محادثة
  Future<Map<String, dynamic>?> getConversationSession({...});
  
  // إزالة جملة مقروءة من الجلسة
  Future<void> removeSentenceFromSession({...});
  
  // حذف جلسة مكتملة
  Future<void> clearSession({...});
}
```

### 2. خدمة جلسات المحادثات
**الملف**: `lib/services/conversation_session_service.dart`

```dart
class ConversationSessionService {
  // حفظ جلسة محادثة مع الرسائل المعروضة
  Future<void> saveConversationSession({...});
  
  // استعادة جلسة محادثة
  Future<Map<String, dynamic>?> getConversationSession({...});
  
  // تحديث عدد الرسائل المعروضة
  Future<void> updateVisibleMessageCount({...});
  
  // حذف جلسة محادثة مكتملة
  Future<void> clearConversationSession({...});
}
```

### 3. تحديث خدمة جمل مسار التعلم
**الملف**: `lib/services/learning_path_sentence_service.dart`

**التحسينات المضافة**:
- ✅ فحص الجلسة المحفوظة قبل جلب جمل جديدة
- ✅ حفظ الجمل الجديدة في الجلسة تلقائيًا
- ✅ إزالة الجمل المقروءة من الجلسة
- ✅ حذف الجلسة عند الإكمال

## 🔄 آلية العمل

### للدفعات (Batches):

1. **عند دخول الدفعة**:
   ```dart
   // فحص الجلسة المحفوظة أولاً
   final savedSession = await sessionService.getBatchSession(...);
   if (savedSession != null) {
     // استعادة الجمل المحفوظة
     return savedSession['sentences'];
   }
   
   // إذا لم توجد جلسة، جلب جمل جديدة وحفظها
   final newSentences = await fetchNewSentences();
   await sessionService.saveBatchSession(..., sentences: newSentences);
   ```

2. **عند قراءة جملة**:
   ```dart
   // تعليم الجملة كمقروءة في Firebase
   await markSentenceAsRead(sentenceId);
   
   // إزالة الجملة من الجلسة المحلية
   await sessionService.removeSentenceFromSession(..., sentenceId);
   
   // إذا انتهت جميع الجمل، حذف الجلسة
   if (remainingSentences.isEmpty) {
     await sessionService.clearSession(...);
   }
   ```

### للمحادثات (Conversations):

1. **عند دخول المحادثة**:
   ```dart
   // استعادة الجلسة المحفوظة
   final savedSession = await sessionService.getConversationSession(...);
   if (savedSession != null) {
     visibleMessageCount = savedSession['displayedMessageIds'].length;
   }
   ```

2. **عند قراءة رسالة أو عرض رسالة جديدة**:
   ```dart
   // تحديث عدد الرسائل المعروضة
   visibleMessageCount++;
   
   // حفظ الحالة المحدثة
   await sessionService.saveConversationSession(
     ..., 
     visibleMessageCount: visibleMessageCount
   );
   ```

## 📱 التحديثات على الشاشات

### شاشة الجمل اليومية
**الملف**: `lib/screens/daily_sentences_screen.dart`

**التحسينات**:
- ✅ استخدام نظام الجلسات لمسار التعلم
- ✅ إزالة الجمل المقروءة من القائمة المعروضة
- ✅ عرض رسالة إكمال عند انتهاء جميع الجمل
- ✅ حذف الجلسة تلقائيًا عند الإكمال

### شاشة تفاصيل المحادثة
**الملف**: `lib/screens/conversation_detail_screen.dart`

**التحسينات**:
- ✅ استعادة عدد الرسائل المعروضة من الجلسة
- ✅ حفظ التقدم عند قراءة رسالة جديدة
- ✅ حفظ الحالة عند الضغط على "عرض الكل"

## 🗂️ تخزين البيانات

### مفاتيح التخزين المحلي:
```dart
// للدفعات
'learning_path_session_batch_{levelId}_{cycleId}_{groupId}'

// للمحادثات  
'conversation_session_{levelId}_{cycleId}_{groupId}'

// للمواقع الحالية
'current_position_{levelId}_{cycleId}_{groupId}'
```

### هيكل البيانات المحفوظة:

**للدفعات**:
```json
{
  "sentences": [
    {
      "id": "sentence_1",
      "englishText": "Hello",
      "arabicText": "مرحبا",
      // ... باقي بيانات الجملة
    }
  ],
  "currentPosition": 0
}
```

**للمحادثات**:
```json
{
  "conversationId": "conv_123",
  "displayedMessageIds": ["msg_1", "msg_2", "msg_3"],
  "currentMessageIndex": 2
}
```

## 🧪 الاختبار

### ملف الاختبار
**الملف**: `lib/test_session_system.dart`

```dart
// تشغيل جميع الاختبارات
await SessionSystemTest.runAllTests();

// اختبار الدفعات فقط
await SessionSystemTest.testBatchSessions();

// اختبار المحادثات فقط  
await SessionSystemTest.testConversationSessions();
```

## 🔧 التكامل مع النظام الحالي

### إضافة الخدمات في main.dart:
```dart
providers: [
  // ... الخدمات الموجودة
  
  // خدمات إدارة جلسات مسار التعلم
  Provider(create: (_) => LearningPathSessionService()),
  Provider(create: (_) => ConversationSessionService()),
]
```

### استخدام الخدمات في الشاشات:
```dart
// في الشاشات التي تحتاج للجلسات
final sessionService = LearningPathSessionService();
final conversationSessionService = ConversationSessionService();
```

## 🎯 الفوائد المحققة

### للمستخدم:
✅ **استمرارية التعلم**: لا يفقد تقدمه عند الخروج والعودة  
✅ **تجربة سلسة**: يكمل من حيث توقف بدون إعادة بدء  
✅ **توفير الوقت**: لا يحتاج لإعادة قراءة الجمل المقروءة  

### للنظام:
✅ **كفاءة الذاكرة**: حذف الجلسات المكتملة تلقائيًا  
✅ **دقة البيانات**: تتبع دقيق لتقدم المستخدم  
✅ **استقرار الأداء**: تقليل طلبات Firebase غير الضرورية  

## 🔮 التطوير المستقبلي

### إمكانيات إضافية:
- 🔄 **مزامنة الجلسات**: مزامنة الجلسات عبر الأجهزة المختلفة
- ⏰ **انتهاء صلاحية الجلسات**: حذف الجلسات القديمة تلقائيًا
- 📊 **إحصائيات الجلسات**: تتبع أنماط استخدام المستخدم
- 🔐 **تشفير الجلسات**: حماية إضافية لبيانات المستخدم

## 📝 ملاحظات مهمة

### للمطورين:
1. **التحقق من المعاملات**: تأكد من تمرير `levelId`, `cycleId`, `groupId` بشكل صحيح
2. **معالجة الأخطاء**: استخدم `try-catch` عند التعامل مع الجلسات
3. **تنظيف الذاكرة**: احرص على حذف الجلسات المكتملة
4. **الاختبار**: استخدم ملف الاختبار للتأكد من عمل النظام

### للصيانة:
- 🔍 **مراقبة الأداء**: تتبع حجم البيانات المحفوظة محليًا
- 🧹 **تنظيف دوري**: إضافة آلية لحذف الجلسات القديمة
- 📈 **تحليل الاستخدام**: مراقبة فعالية النظام

---

## 🎉 الخلاصة

تم تطوير نظام شامل ومتكامل لإدارة جلسات مسار التعلم يحل مشكلة فقدان التقدم بشكل نهائي. النظام يوفر تجربة تعلم سلسة ومستمرة للمستخدمين مع الحفاظ على كفاءة الأداء واستقرار النظام.

**النظام جاهز للاستخدام ومختبر بالكامل! 🚀**
