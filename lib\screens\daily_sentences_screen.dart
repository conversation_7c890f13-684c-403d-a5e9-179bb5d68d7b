import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/points.dart';
import '../models/sentence_model.dart';
import '../providers/level_provider.dart';
import '../providers/points_provider.dart';
import '../viewmodels/sentence_view_model.dart';
import '../widgets/quiz_dialog.dart';
import '../services/points_animation_service.dart';
import '../services/learning_path_sentence_service.dart';
import '../services/hive_sentence_service.dart';

class DailySentencesScreen extends StatefulWidget {
  static const routeName = '/daily-sentences';

  final int? levelId;
  final int? cycleId;
  final int? groupId;
  final String? title;

  const DailySentencesScreen({
    Key? key,
    this.levelId,
    this.cycleId,
    this.groupId,
    this.title,
  }) : super(key: key);

  @override
  State<DailySentencesScreen> createState() => _DailySentencesScreenState();
}

class _DailySentencesScreenState extends State<DailySentencesScreen> {
  bool _isLoading = false;
  int _levelId = 0;
  int _cycleId = 0;
  int _groupId = 0;
  String _title = '';
  late PageController _pageController;

  // Map para controlar la visibilidad de las traducciones
  final Map<String, bool> _showTranslations = {};
  int _completedSentences = 0;
  double _accuracy = 0.0;
  int _totalSentences = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    // تهيئة خدمة جمل مسار التعلم
    _learningPathSentenceService = LearningPathSentenceService(
      hiveSentenceService: HiveSentenceService(),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Primero intentamos obtener los parámetros del widget
    if (widget.levelId != null &&
        widget.cycleId != null &&
        widget.groupId != null &&
        widget.title != null) {
      _levelId = widget.levelId!;
      _cycleId = widget.cycleId!;
      _groupId = widget.groupId!;
      _title = widget.title!;
      _loadSentences();
      return;
    }

    // Si no hay parámetros en el widget, intentamos obtenerlos de los argumentos de la ruta
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null) {
      _levelId = args['levelId'] as int;
      _cycleId = args['cycleId'] as int? ?? 0;
      _groupId = args['groupId'] as int;
      _title = args['title'] as String;
      _loadSentences();
    }
  }

  // تحديد ما إذا كانت الشاشة جزءًا من مسار التعلم
  bool get _isPartOfLearningPath => _levelId > 0 && _groupId > 0;

  // خدمة جمل مسار التعلم
  late LearningPathSentenceService _learningPathSentenceService;

  // قائمة الجمل المستخدمة في مسار التعلم
  List<SentenceModel> _learningPathSentences = [];

  Future<void> _loadSentences() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // الحصول على معلومات المجموعة من مزود المستويات
      LevelProvider levelProvider;
      try {
        levelProvider = Provider.of<LevelProvider>(context, listen: false);
      } catch (e) {
        // إذا لم يكن المزود موجودًا، قم بإنشاء مزود محلي
        debugPrint('LevelProvider not found, creating a local instance');
        levelProvider = LevelProvider();
        // تحميل المستويات
        await levelProvider.fetchLevels();
      }

      final level = levelProvider.getLevelById(_levelId);

      if (level != null) {
        // البحث عن الدورة
        final cycle = level.cycles.firstWhere(
          (cycle) => cycle.id == _cycleId,
          orElse: () => throw Exception('الدورة غير موجودة'),
        );

        // البحث عن المجموعة داخل الدورة
        final lessonGroup = cycle.lessonGroups.firstWhere(
          (group) => group.id == _groupId,
          orElse: () => throw Exception('مجموعة الدروس غير موجودة'),
        );

        _completedSentences = lessonGroup.completedSentences;
        _accuracy = lessonGroup.accuracy;
        _totalSentences = lessonGroup.totalSentences;

        // إذا كانت الشاشة جزءًا من مسار التعلم، استخدم خدمة جمل مسار التعلم
        if (_isPartOfLearningPath) {
          debugPrint(
              'جلب جمل مسار التعلم للمستوى $_levelId، الدورة $_cycleId، المجموعة $_groupId');
          _learningPathSentences =
              await _learningPathSentenceService.getLearningPathGroupSentences(
            _levelId,
            _cycleId,
            _groupId,
          );
          debugPrint(
              'تم جلب ${_learningPathSentences.length} جملة لمسار التعلم');
        }
      }

      // إذا لم تكن الشاشة جزءًا من مسار التعلم، استخدم SentenceViewModel
      if (!_isPartOfLearningPath && mounted) {
        final sentenceViewModel =
            Provider.of<SentenceViewModel>(context, listen: false);
        await sentenceViewModel.loadRandomDailySentences();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الجمل: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // بناء معلومات التقدم
  Widget _buildProgressInfo() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المستوى $_levelId - $_title',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  '$_completedSentences/$_totalSentences',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: _totalSentences > 0
                  ? _completedSentences / _totalSentences
                  : 0,
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(
                _completedSentences == _totalSentences
                    ? Colors.green
                    : Colors.blue,
              ),
              minHeight: 8,
              borderRadius: BorderRadius.circular(4),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('دقة النطق:'),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getAccuracyColor().withAlpha(25),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${(_accuracy * 100).toInt()}%',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _getAccuracyColor(),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة الجملة
  Widget _buildSentenceCard(
      SentenceModel sentence, int index, bool isLearningPath) {
    // الحصول على viewModel إذا لم تكن جزءًا من مسار التعلم
    final viewModel = isLearningPath
        ? null
        : Provider.of<SentenceViewModel>(context, listen: false);

    // حالة زر "تم القراءة" - سيتم تعطيله إذا كانت الجملة مقروءة بالفعل أو لم يتم اختبار النطق
    final bool isReadButtonDisabled =
        !sentence.isPronunciationTested || sentence.isReadByCurrentUser;

    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // فئة الجملة
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.blue.withAlpha(25),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    sentence.category,
                    style: const TextStyle(
                      color: Colors.blue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                // مستوى الصعوبة
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color:
                        _getDifficultyColor(sentence.difficulty).withAlpha(25),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _getDifficultyText(sentence.difficulty),
                    style: TextStyle(
                      color: _getDifficultyColor(sentence.difficulty),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // نص الجملة بالإنجليزية
            Text(
              sentence.englishText,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.left,
              textDirection: TextDirection.ltr,
            ),
            const SizedBox(height: 16),
            // نص الجملة بالعربية (مخفي بشكل افتراضي)
            _showTranslations[sentence.id] == true
                ? Text(
                    sentence.arabicText,
                    style: const TextStyle(
                      fontSize: 18,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.right,
                    textDirection: TextDirection.rtl,
                  )
                : ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _showTranslations[sentence.id] = true;
                      });
                    },
                    icon: const Icon(Icons.translate),
                    label: const Text('عرض الترجمة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey.shade200,
                      foregroundColor: Colors.black87,
                    ),
                  ),
            const Spacer(),
            // أزرار التفاعل
            Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // زر الاختبار
                    ElevatedButton.icon(
                      onPressed: sentence.isReadByCurrentUser
                          ? null // تعطيل زر الاختبار إذا كانت الجملة مقروءة بالفعل
                          : () {
                              // فتح نافذة الاختبار
                              // حفظ حالة الاختبار قبل فتح النافذة
                              final wasTested = sentence.isPronunciationTested;

                              showDialog(
                                context: context,
                                builder: (context) => QuizDialog(
                                  sentence: sentence,
                                ),
                              ).then((result) {
                                // التحقق من نتيجة الاختبار
                                if (result != null &&
                                    result is Map<String, dynamic>) {
                                  // تحديث حالة الاختبار في نموذج الجملة
                                  if (result
                                      .containsKey('isPronunciationTested')) {
                                    sentence.isPronunciationTested =
                                        result['isPronunciationTested'];

                                    // طباعة معلومات التصحيح
                                    final hasTested =
                                        result['hasTested'] ?? false;
                                    final passedTest =
                                        result['passedTest'] ?? false;
                                    final pronunciationScore =
                                        result['pronunciationScore'] ?? 0.0;
                                    final memoryScore =
                                        result['memoryScore'] ?? 0.0;
                                    final averageScore =
                                        (pronunciationScore + memoryScore) / 2;

                                    debugPrint('نتيجة الاختبار:');
                                    debugPrint('- تم الاختبار: $hasTested');
                                    debugPrint('- اجتاز الاختبار: $passedTest');
                                    debugPrint(
                                        '- نتيجة النطق: ${(pronunciationScore * 100).toInt()}%');
                                    debugPrint(
                                        '- نتيجة الحفظ: ${(memoryScore * 100).toInt()}%');
                                    debugPrint(
                                        '- متوسط النتيجة: ${(averageScore * 100).toInt()}%');

                                    // تحديث واجهة المستخدم
                                    setState(() {});
                                  }
                                }

                                // بعد إغلاق نافذة الاختبار، تحديث النقاط فقط إذا تغيرت حالة الاختبار
                                // وإذا لم تكن الجملة مقروءة بالفعل
                                if (!wasTested &&
                                    sentence.isPronunciationTested &&
                                    !sentence.isReadByCurrentUser) {
                                  debugPrint(
                                      'Updating points after quiz - sentence was newly tested');
                                  _updatePointsAfterQuiz(sentence);
                                } else {
                                  debugPrint(
                                      'Not updating points - sentence was already tested or test not completed or already read');
                                }
                              });
                            },
                      icon: const Icon(Icons.mic),
                      label: const Text('اختبار'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        disabledBackgroundColor: Colors.grey.shade300,
                        disabledForegroundColor: Colors.grey.shade600,
                      ),
                    ),
                    // زر تعليم كمقروءة
                    ElevatedButton.icon(
                      onPressed: isReadButtonDisabled
                          ? null // تعطيل الزر إذا لم يتم اختبار النطق أو إذا كانت الجملة مقروءة بالفعل
                          : () {
                              // تعطيل الزر فورًا بعد الضغط عليه
                              setState(() {
                                sentence.isReadByCurrentUser = true;
                              });
                              // ثم تنفيذ عملية تعليم الجملة كمقروءة
                              _markSentenceAsRead(sentence, viewModel);
                            },
                      icon: const Icon(Icons.check),
                      label: sentence.isReadByCurrentUser
                          ? const Text('تمت القراءة')
                          : const Text('تم القراءة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        // تغيير لون الزر عندما يكون معطلاً
                        disabledBackgroundColor: sentence.isReadByCurrentUser
                            ? Colors.green.withAlpha(
                                179) // 0.7 * 255 = 179 (لون مختلف للجمل المقروءة)
                            : Colors.grey.shade300,
                        disabledForegroundColor: sentence.isReadByCurrentUser
                            ? Colors.white.withAlpha(
                                230) // 0.9 * 255 = 230 (لون مختلف للجمل المقروءة)
                            : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),

                // زر محاكاة نجاح الاختبار (للتجريب فقط)
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.amber.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.amber.withAlpha(128)),
                  ),
                  child: Column(
                    children: [
                      const Text(
                        'للتجريب فقط',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.amber,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      ElevatedButton.icon(
                        onPressed: !sentence.isPronunciationTested &&
                                !sentence.isReadByCurrentUser
                            ? () => _simulateSuccessfulQuiz(sentence)
                            : null,
                        icon: const Icon(Icons.science, size: 16),
                        label: const Text('محاكاة نجاح الاختبار'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.amber,
                          foregroundColor: Colors.black87,
                          minimumSize: const Size(double.infinity, 36),
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          textStyle: const TextStyle(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // عرض رسالة عند قراءة جميع الجمل
  Widget _buildAllSentencesReadView(bool isLearningPath) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.check_circle,
            size: 64,
            color: Colors.green,
          ),
          const SizedBox(height: 16),
          Text(
            'لقد قمت بقراءة الجمل واصل التعلم',
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.symmetric(horizontal: 24),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: const Text(
              'يمكنك الآن العودة إلى مسار التعلم ومواصلة الدروس الأخرى في المجموعة الحالية',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ),
          if (!isLearningPath) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadSentences,
              icon: const Icon(Icons.refresh),
              label: const Text('تحميل جمل جديدة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // تعليم الجملة كمقروءة وتحديث النقاط
  Future<void> _markSentenceAsRead(
      SentenceModel sentence, SentenceViewModel? viewModel) async {
    if (_isPartOfLearningPath) {
      // إذا كانت جزءًا من مسار التعلم، استخدم خدمة جمل مسار التعلم
      await _learningPathSentenceService.markSentenceAsRead(
        sentence.id,
        _levelId,
        _cycleId,
        _groupId,
      );

      // إزالة الجملة من قائمة الجمل المعروضة
      setState(() {
        _learningPathSentences.removeWhere((s) => s.id == sentence.id);
      });

      // التحقق من إكمال جميع الجمل
      if (_learningPathSentences.isEmpty) {
        await _learningPathSentenceService.clearCompletedSession(
          _levelId,
          _cycleId,
          _groupId,
        );

        // عرض رسالة إكمال الدفعة
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تهانينا! تم إكمال جميع جمل الدفعة بنجاح!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } else if (viewModel != null) {
      // وإلا استخدم SentenceViewModel
      await viewModel.markAsRead(sentence);
    }

    // تحديث النقاط
    await _updatePointsAfterReading(sentence);

    // تحديث واجهة المستخدم
    setState(() {
      _completedSentences++;
    });
  }

  // تحديث النقاط بعد قراءة الجملة
  Future<void> _updatePointsAfterReading(SentenceModel sentence) async {
    final levelProvider = Provider.of<LevelProvider>(context, listen: false);
    final pointsProvider = Provider.of<PointsProvider>(context, listen: false);

    // إضافة 10 نقاط لقراءة الجملة
    await pointsProvider.addPoints(
      10,
      PointType.educational,
      'قراءة جملة في المستوى $_levelId، الدورة $_cycleId، المجموعة $_groupId',
    );

    // عرض تأثير النقاط
    if (mounted) {
      PointsAnimationService().showPointsAnimation(
        context,
        10,
        PointType.educational,
      );
    }

    // حساب ما إذا كانت المجموعة مكتملة
    final newCompletedSentences = _completedSentences + 1;
    final isGroupCompleted = newCompletedSentences >= _totalSentences;

    // تحديث تقدم المجموعة
    await levelProvider.updateLessonGroupProgress(
      _levelId,
      _cycleId, // إضافة معرف الدورة
      _groupId,
      newCompletedSentences,
      _accuracy,
      isGroupCompleted,
    );

    // إذا تم إكمال المجموعة، عرض رسالة تهنئة
    if (isGroupCompleted && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تهانينا! تم إكمال المجموعة بنجاح!'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );
    }

    // تحديث تقدم المستوى
    final level = levelProvider.getLevelById(_levelId);
    if (level != null) {
      final earnedPoints = level.earnedEducationalPoints + 10;
      await levelProvider.updateLevelProgress(
        _levelId,
        _completedSentences + 1,
        earnedPoints,
      );
    }
  }

  // محاكاة نجاح الاختبار للتجريب
  Future<void> _simulateSuccessfulQuiz(SentenceModel sentence) async {
    // تعليم الجملة كمختبرة بنجاح
    sentence.isPronunciationTested = true;

    debugPrint('تم محاكاة نجاح الاختبار للجملة: ${sentence.id}');

    // تحديث النقاط بعد الاختبار
    await _updatePointsAfterQuiz(sentence);

    // تحديث واجهة المستخدم
    setState(() {});

    // عرض رسالة نجاح
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم محاكاة نجاح الاختبار بنجاح!'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  // تحديث النقاط بعد إجراء الاختبار
  Future<void> _updatePointsAfterQuiz(SentenceModel sentence) async {
    // تأكد من أن الجملة تم اختبارها بالفعل وأنها ليست مقروءة بالفعل
    if (!sentence.isPronunciationTested) {
      debugPrint('Skipping points update - sentence is not marked as tested');
      return;
    }

    // تأكد من أن الجملة ليست مقروءة بالفعل
    if (sentence.isReadByCurrentUser) {
      debugPrint('Skipping points update - sentence is already read');
      return;
    }

    final levelProvider = Provider.of<LevelProvider>(context, listen: false);
    final pointsProvider = Provider.of<PointsProvider>(context, listen: false);

    debugPrint('Adding 5 points for completing pronunciation test');

    // إضافة 5 نقاط لإجراء اختبار الحفظ
    await pointsProvider.addPoints(
      5,
      PointType.educational,
      'اختبار حفظ في المستوى $_levelId، الدورة $_cycleId، المجموعة $_groupId',
    );

    // عرض تأثير النقاط
    if (mounted) {
      PointsAnimationService().showPointsAnimation(
        context,
        5,
        PointType.educational,
      );
    }

    // تحديث دقة النطق - استخدام قيمة أكثر واقعية بناءً على نتيجة الاختبار
    // نفترض أن متوسط الدقة هو 0.85 (يمكن تعديله لاحقًا)
    final newAccuracy =
        (_accuracy * _completedSentences + 0.85) / (_completedSentences + 1);

    // تحديث تقدم المجموعة
    await levelProvider.updateLessonGroupProgress(
      _levelId,
      _cycleId, // إضافة معرف الدورة
      _groupId,
      _completedSentences,
      newAccuracy,
      _completedSentences >= _totalSentences,
    );

    // تحديث تقدم المستوى
    final level = levelProvider.getLevelById(_levelId);
    if (level != null) {
      final earnedPoints = level.earnedEducationalPoints + 5;
      await levelProvider.updateLevelProgress(
        _levelId,
        _completedSentences,
        earnedPoints,
      );
    }

    // تحديث واجهة المستخدم
    setState(() {
      _accuracy = newAccuracy;
    });

    debugPrint('Points updated successfully after quiz');
  }

  // الحصول على لون مستوى الصعوبة
  Color _getDifficultyColor(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // الحصول على نص مستوى الصعوبة
  String _getDifficultyText(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return 'سهل';
      case 'medium':
        return 'متوسط';
      case 'hard':
        return 'صعب';
      default:
        return 'غير محدد';
    }
  }

  // الحصول على لون دقة النطق
  Color _getAccuracyColor() {
    if (_accuracy >= 0.8) {
      return Colors.green;
    } else if (_accuracy >= 0.6) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحقق من وجود المزود
    try {
      Provider.of<LevelProvider>(context, listen: false);
    } catch (e) {
      // إذا لم يكن المزود موجودًا، قم بإنشاء مزود محلي
      return ChangeNotifierProvider<LevelProvider>(
        create: (_) => LevelProvider(),
        child: _buildScaffold(context),
      );
    }

    // إذا كان المزود موجودًا، استخدمه مباشرة
    return _buildScaffold(context);
  }

  Widget _buildScaffold(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_title),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSentences,
            tooltip: 'تحديث الجمل',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Builder(
              builder: (context) {
                // تحديد قائمة الجمل المناسبة
                List<SentenceModel> sentences = [];

                if (_isPartOfLearningPath) {
                  // استخدام جمل مسار التعلم
                  sentences = _learningPathSentences;
                } else {
                  // استخدام الجمل اليومية من SentenceViewModel
                  final sentenceViewModel =
                      Provider.of<SentenceViewModel>(context);
                  sentences = sentenceViewModel.dailyRandomSentences;
                }

                if (sentences.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.info_outline,
                          size: 64,
                          color: Colors.blue,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد جمل متاحة حاليًا',
                          style: Theme.of(context).textTheme.headlineSmall,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'اضغط على زر التحديث لتحميل جمل جديدة',
                          style: TextStyle(fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: _loadSentences,
                          icon: const Icon(Icons.refresh),
                          label: const Text('تحميل جمل جديدة'),
                        ),
                      ],
                    ),
                  );
                }

                // تصفية الجمل المقروءة بالفعل
                final unreadSentences = sentences
                    .where((sentence) => !sentence.isReadByCurrentUser)
                    .toList();

                return Column(
                  children: [
                    // معلومات التقدم
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: _buildProgressInfo(),
                    ),

                    // عرض الجمل
                    Expanded(
                      child: unreadSentences.isEmpty
                          ? _buildAllSentencesReadView(_isPartOfLearningPath)
                          : PageView.builder(
                              controller: _pageController,
                              itemCount: unreadSentences.length,
                              scrollDirection: Axis.horizontal,
                              physics: const BouncingScrollPhysics(),
                              itemBuilder: (context, index) {
                                return _buildSentenceCard(
                                  unreadSentences[index],
                                  index,
                                  _isPartOfLearningPath,
                                );
                              },
                            ),
                    ),
                  ],
                );
              },
            ),
    );
  }
}
