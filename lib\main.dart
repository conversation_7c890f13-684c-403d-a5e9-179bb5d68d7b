import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:test05/services/file_cache_service.dart';
import 'package:test05/services/firestore_service.dart';
import 'firebase_options.dart';
import 'screens/splash_screen.dart';
import 'screens/conversations_screen.dart';
import 'screens/read_conversations_screen.dart';
import 'screens/create_conversation_screen.dart';
import 'screens/create_conversation_bulk_screen.dart';
import 'screens/calendar_stats_screen.dart';
import 'screens/review_screen.dart';
import 'screens/timeline_screen_new.dart';
import 'screens/daily_sentences_screen.dart';
import 'screens/conversation_screen.dart';
import 'screens/test_cycles_screen.dart';
import 'screens/admin/admin_dashboard_screen.dart';
import 'screens/admin/points_settings_screen.dart';
import 'screens/admin/levels_settings_screen.dart';
import 'viewmodels/auth_view_model.dart';
import 'viewmodels/settings_view_model.dart';
import 'viewmodels/sentence_view_model.dart';
import 'viewmodels/hive_sentence_view_model.dart';
import 'viewmodels/new_sentence_view_model.dart';
import 'services/connectivity_service.dart';
import 'services/sentence_service.dart';
import 'services/background_sync_service.dart';
import 'services/native_notification_service.dart';
import 'services/sentence_listener_service.dart';
import 'services/local_sentence_storage_service.dart';
import 'services/hive_service.dart';
import 'services/hive_sentence_service.dart';
import 'services/hive_date_service.dart';
import 'services/sync_service.dart';
import 'services/sync_manager.dart';
import 'services/daily_sentence_service.dart';
import 'services/displayed_sentences_service.dart';
import 'services/new_daily_sentence_service.dart';
import 'services/daily_sentences_manager.dart';
import 'services/conversation_service.dart';
import 'services/category_service.dart';
import 'services/learning_path_session_service.dart';
import 'services/conversation_session_service.dart';
import 'theme/app_theme.dart';
import 'providers/level_provider.dart';
import 'providers/points_provider.dart';
import 'utils/firebase_setup.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

Future<void> main() async {
  // تهيئة Flutter binding قبل أي شيء آخر
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();

  // الحفاظ على شاشة البداية حتى اكتمال التهيئة الأساسية
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  try {
    // تهيئة Firebase بشكل متزامن قبل أي شيء آخر
    debugPrint('جاري تهيئة Firebase...');
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    ).timeout(
      const Duration(seconds: 10),
      onTimeout: () {
        debugPrint('تحذير: تهيئة Firebase تجاوزت المهلة، سيتم إعادة المحاولة');
        return Firebase.initializeApp();
      },
    );
    debugPrint('تم تهيئة Firebase بنجاح');

    // تهيئة Google Mobile Ads SDK
    await MobileAds.instance.initialize();
    debugPrint('تم تهيئة Google Mobile Ads بنجاح');

    // تهيئة Hive - ضروري للتخزين المحلي
    await HiveService.init();
    debugPrint('تم تهيئة Hive بنجاح');

    // تهيئة SharedPreferences - ضروري للإعدادات
    final prefs = await SharedPreferences.getInstance();

    // بدء تشغيل التطبيق مع تهيئة الخدمات الأساسية
    runApp(MyApp(prefs: prefs));

    // إزالة شاشة البداية بعد بدء التطبيق مباشرة
    FlutterNativeSplash.remove();

    // تأجيل تهيئة الخدمات غير الضرورية إلى ما بعد تشغيل التطبيق
    Future.delayed(const Duration(milliseconds: 500), () async {
      try {
        // تهيئة خدمة الإشعارات
        final notificationService = NativeNotificationService();
        await notificationService.scheduleAllNotifications();

        // تهيئة خدمة مراقبة الجمل
        final sentenceListenerService = SentenceListenerService();
        sentenceListenerService.initialize();

        // تهيئة خدمة تخزين الجمل المحلية
        LocalSentenceStorageService();

        // إعداد بيانات المستويات والنقاط في Firebase
        await FirebaseSetup.setupAllData();

        debugPrint('تم تهيئة الخدمات الإضافية بنجاح');
      } catch (e) {
        debugPrint('خطأ في تهيئة الخدمات الإضافية: $e');
      }
    });
  } catch (e) {
    // إزالة شاشة البداية في حالة الخطأ
    FlutterNativeSplash.remove();

    debugPrint('خطأ في تهيئة التطبيق: $e');
    runApp(const ErrorApp(error: 'فشل في تهيئة التطبيق'));
  }
}

class ErrorApp extends StatelessWidget {
  final String error;
  const ErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 60,
                ),
                const SizedBox(height: 20),
                Text(
                  error,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    // إعادة تشغيل التطبيق
                    main();
                  },
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  final SharedPreferences prefs;

  const MyApp({super.key, required this.prefs});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthViewModel()),
        ChangeNotifierProvider(create: (_) => SettingsViewModel(prefs)),
        ChangeNotifierProvider(create: (_) => ConnectivityService()),
        Provider(create: (_) => FirestoreService()),
        Provider(create: (_) => FileCacheService()),
        Provider(create: (_) => LocalSentenceStorageService()),

        // Nuevos servicios de Hive
        Provider(create: (_) => HiveSentenceService()),
        Provider(create: (_) => HiveDateService()),
        Provider(create: (_) => DisplayedSentencesService()),
        Provider(
          create: (context) => SyncService(
            context.read<HiveSentenceService>(),
          ),
        ),
        Provider(
          create: (context) => SyncManager(
            context.read<HiveSentenceService>(),
            context.read<SyncService>(),
          ),
          dispose: (_, manager) => manager.dispose(),
        ),
        Provider(
          create: (context) => DailySentenceService(
            context.read<HiveSentenceService>(),
            context.read<HiveDateService>(),
            context.read<SyncService>(),
          ),
        ),
        Provider(
          create: (context) => NewDailySentenceService(
            hiveSentenceService: context.read<HiveSentenceService>(),
            syncService: context.read<SyncService>(),
          ),
        ),
        Provider(
          create: (_) => DailySentencesManager(),
        ),

        // خدمات إدارة جلسات مسار التعلم
        Provider(create: (_) => LearningPathSessionService()),
        Provider(create: (_) => ConversationSessionService()),

        // Servicios existentes
        Provider(
            create: (context) => SentenceService(
                cacheService: context.read<FileCacheService>(),
                localStorageService:
                    context.read<LocalSentenceStorageService>())),
        ChangeNotifierProvider<SentenceViewModel>(
          create: (context) => SentenceViewModel(
            context.read<FirestoreService>(),
            context.read<FileCacheService>(),
            context.read<SentenceService>(),
            context.read<AuthViewModel>(),
            localStorageService: context.read<LocalSentenceStorageService>(),
            hiveSentenceService: context.read<HiveSentenceService>(),
            syncManager: context.read<SyncManager>(),
            dailySentenceService: context.read<DailySentenceService>(),
            dailySentencesManager: context.read<DailySentencesManager>(),
          ),
        ),
        Provider(
          create: (context) => BackgroundSyncService(
            connectivityService: context.read<ConnectivityService>(),
            sentenceService: context.read<SentenceService>(),
            authViewModel: context.read<AuthViewModel>(),
          ),
          dispose: (_, service) => service.dispose(),
        ),
        Provider(
          create: (_) => NativeNotificationService(),
        ),

        // Proveedor para el servicio de conversaciones
        Provider(
          create: (_) => ConversationService(),
        ),

        // Proveedor para el servicio de categorías
        Provider(
          create: (_) => CategoryService(),
        ),

        // Nuevo ViewModel para Hive
        ChangeNotifierProvider<HiveSentenceViewModel>(
          create: (context) => HiveSentenceViewModel(
            context.read<HiveSentenceService>(),
            context.read<DailySentenceService>(),
            context.read<SyncManager>(),
          ),
        ),

        // Nuevo ViewModel con la nueva implementación
        ChangeNotifierProvider<NewSentenceViewModel>(
          create: (context) => NewSentenceViewModel(
            hiveSentenceService: context.read<HiveSentenceService>(),
            syncManager: context.read<SyncManager>(),
            syncService: context.read<SyncService>(),
            newDailySentenceService: context.read<NewDailySentenceService>(),
            displayedSentencesService:
                context.read<DisplayedSentencesService>(),
          ),
        ),

        // مزودي المستويات والنقاط
        ChangeNotifierProvider<LevelProvider>(
          create: (_) => LevelProvider(),
        ),
        ChangeNotifierProvider<PointsProvider>(
          create: (_) => PointsProvider(),
        ),
      ],
      child: Consumer<SettingsViewModel>(
        builder: (context, settingsVM, _) {
          return MaterialApp(
            title: '10 Again',
            debugShowCheckedModeBanner: false,
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('ar', 'DZ'),
            ],
            locale: const Locale('ar', 'DZ'),
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: settingsVM.themeMode,
            home: const SplashScreen(),
            routes: {
              '/conversations': (context) => const ConversationsScreen(),
              '/read_conversations': (context) =>
                  const ReadConversationsScreen(),
              '/create_conversation': (context) =>
                  const CreateConversationScreen(),
              '/create_conversation_bulk': (context) =>
                  const CreateConversationBulkScreen(),
              '/calendar-stats': (context) => const CalendarStatsScreen(),
              '/timeline': (context) => const TimelineScreen(),
              '/daily-sentences': (context) => const DailySentencesScreen(),
              '/conversation': (context) => const ConversationScreen(),
              '/review': (context) => const ReviewScreen(
                    levelId: 1,
                    cycleId: 1,
                    groupId: 4, // مجموعة المراجعة في المستوى الأول
                    title: 'المراجعة',
                    showGroups: true, // عرض بطاقات المجموعات
                  ),
              '/admin/dashboard': (context) => const AdminDashboardScreen(),
              '/admin/points-settings': (context) =>
                  const PointsSettingsScreen(),
              '/admin/levels-settings': (context) =>
                  const LevelsSettingsScreen(),
              '/test-cycles': (context) => const TestCyclesScreen(),
            },
          );
        },
      ),
    );
  }
}
