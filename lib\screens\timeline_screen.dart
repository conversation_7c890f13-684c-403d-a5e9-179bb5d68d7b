import 'package:flutter/material.dart';
import 'package:timeline_tile/timeline_tile.dart';
import 'package:provider/provider.dart';
import '../models/level.dart';
import '../models/lesson_group.dart';
import '../models/cycle.dart';
import '../providers/level_provider.dart';
import '../providers/points_provider.dart';
import '../utils/app_colors.dart';
import '../services/level_setup_service.dart';
import 'daily_sentences_screen.dart';
import 'conversation_screen.dart';
import 'review_screen.dart';

class TimelineScreen extends StatefulWidget {
  static const routeName = '/timeline';

  const TimelineScreen({Key? key}) : super(key: key);

  @override
  State<TimelineScreen> createState() => _TimelineScreenState();
}

class _TimelineScreenState extends State<TimelineScreen> {
  bool _isLoading = true;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _loadData();
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل بيانات المستويات والنقاط
      final levelProvider = Provider.of<LevelProvider>(context, listen: false);
      final pointsProvider =
          Provider.of<PointsProvider>(context, listen: false);

      await Future.wait([
        levelProvider.fetchLevels(),
        pointsProvider.fetchPoints(),
      ]);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      // التمرير إلى المستوى الحالي بعد تحميل البيانات
      if (mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToCurrentLevel(levelProvider.levels);
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');

      // إذا كان الخطأ بسبب عدم وجود المزود، قم بإعادة بناء الشاشة
      if (e.toString().contains('Provider<LevelProvider>')) {
        if (mounted) {
          setState(() {}); // إعادة بناء الشاشة
        }
      } else {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  // دالة إعادة هيكلة المستويات
  Future<void> _restructureLevels() async {
    // عرض مربع حوار للتأكيد
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة هيكلة المستويات'),
        content: const Text(
          'هل أنت متأكد من أنك تريد إعادة هيكلة المستويات؟ سيتم إنشاء المستويات والدورات وفقًا للهيكل المعتمد.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );

    if (confirm != true || !mounted) return;

    // عرض مؤشر التحميل
    setState(() {
      _isLoading = true;
    });

    try {
      // استدعاء خدمة إعادة هيكلة المستويات
      final levelSetupService = LevelSetupService();
      await levelSetupService.restructureLevels();

      // إعادة تحميل البيانات
      await _loadData();

      if (mounted) {
        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إعادة هيكلة المستويات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في إعادة هيكلة المستويات: $e');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // عرض رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إعادة هيكلة المستويات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _scrollToCurrentLevel(List<Level> levels) {
    final currentLevelIndex = levels.indexWhere((level) => level.isCurrent);
    if (currentLevelIndex != -1) {
      final scrollPosition = currentLevelIndex * 200.0; // تقدير تقريبي للموضع
      _scrollController.animateTo(
        scrollPosition,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تحقق من وجود المزود
    try {
      Provider.of<LevelProvider>(context, listen: false);
    } catch (e) {
      // إذا لم يكن المزود موجودًا، قم بإنشاء مزود محلي
      return ChangeNotifierProvider<LevelProvider>(
        create: (_) => LevelProvider(),
        child: _buildScaffold(context),
      );
    }

    // إذا كان المزود موجودًا، استخدمه مباشرة
    return _buildScaffold(context);
  }

  Widget _buildScaffold(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مسار التعلم'),
        centerTitle: true,
        actions: [
          // زر إعادة هيكلة المستويات
          IconButton(
            icon: const Icon(Icons.settings_backup_restore),
            tooltip: 'إعادة هيكلة المستويات',
            onPressed: _restructureLevels,
          ),
          // زر تحديث البيانات
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Consumer<LevelProvider>(
              builder: (ctx, levelProvider, _) {
                final levels = levelProvider.levels;
                if (levels.isEmpty) {
                  return const Center(
                    child: Text('لا توجد مستويات متاحة حاليًا'),
                  );
                }

                return RefreshIndicator(
                  onRefresh: _loadData,
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(
                        vertical: 20, horizontal: 16),
                    itemCount: levels.length,
                    itemBuilder: (ctx, index) {
                      final level = levels[index];
                      return _buildLevelTimelineTile(
                          level, index == 0, index == levels.length - 1);
                    },
                  ),
                );
              },
            ),
    );
  }

  Widget _buildLevelTimelineTile(Level level, bool isFirst, bool isLast) {
    return TimelineTile(
      alignment: TimelineAlign.manual,
      lineXY: 0.2,
      isFirst: isFirst,
      isLast: isLast,
      indicatorStyle: IndicatorStyle(
        width: 40,
        height: 40,
        indicator: _buildLevelIndicator(level),
        drawGap: true,
      ),
      beforeLineStyle: LineStyle(
        color: level.isLocked
            ? Colors.grey.shade300
            : AppColors.getLevelColor(level.id),
        thickness: 3,
      ),
      afterLineStyle: LineStyle(
        color: level.isLocked
            ? Colors.grey.shade300
            : AppColors.getLevelColor(level.id),
        thickness: 3,
      ),
      endChild: _buildLevelContent(level),
    );
  }

  Widget _buildLevelIndicator(Level level) {
    return Container(
      decoration: BoxDecoration(
        color: level.isLocked
            ? Colors.grey.shade300
            : level.isCurrent
                ? AppColors.getLevelColor(level.id)
                : Colors.green,
        shape: BoxShape.circle,
        border: Border.all(
          color: level.isCurrent ? AppColors.accentColor : Colors.transparent,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: level.isLocked
                ? Colors.grey.withAlpha(76) // 0.3 * 255 = 76
                : AppColors.getLevelColor(level.id)
                    .withAlpha(128), // 0.5 * 255 = 128
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Center(
        child: level.isLocked
            ? const Icon(Icons.lock, color: Colors.white, size: 20)
            : Text(
                '${level.id}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
      ),
    );
  }

  Widget _buildLevelContent(Level level) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 8, top: 8, bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان المستوى وشريط التقدم
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: level.isLocked
                  ? Colors.grey.shade100
                  : AppColors.getLevelColor(level.id).withAlpha(30),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'المستوى ${level.id}: ${level.title}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              color:
                                  level.isLocked ? Colors.grey : Colors.black,
                            ),
                          ),
                          if (!level.isLocked) ...[
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Text(
                                  'الصعوبة: ',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                Text(
                                  _getDifficultyText(level.difficulty),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color:
                                        _getDifficultyColor(level.difficulty),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                    if (!level.isLocked)
                      Consumer<PointsProvider>(
                        builder: (ctx, pointsProvider, _) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: AppColors.getLevelColor(level.id)
                                  .withAlpha(50),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.star,
                                  color: AppColors.getLevelColor(level.id),
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${level.earnedEducationalPoints}/${level.totalEducationalPoints}',
                                  style: TextStyle(
                                    color: AppColors.getLevelColor(level.id),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                  ],
                ),
                if (!level.isLocked) ...[
                  const SizedBox(height: 8),
                  // شريط التقدم
                  ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: LinearProgressIndicator(
                      value: level.earnedEducationalPoints /
                          level.totalEducationalPoints,
                      backgroundColor: Colors.grey.shade200,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        level.isCurrent
                            ? AppColors.getLevelColor(level.id)
                            : Colors.green,
                      ),
                      minHeight: 8,
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 16),
          if (!level.isLocked) ...[
            // عنوان الدورات
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Row(
                children: [
                  const Icon(Icons.timeline, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'الدورات (${level.cycles.length})',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            // عرض الدورات
            ...level.cycles.map((cycle) => _buildCycleCard(level, cycle)),
          ] else ...[
            // رسالة المستوى المغلق
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      shape: BoxShape.circle,
                    ),
                    child:
                        const Icon(Icons.lock, color: Colors.white, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'هذا المستوى مغلق',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'أكمل ${level.requiredSentences} جملة لفتح هذا المستوى',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // بناء بطاقة الدورة
  Widget _buildCycleCard(Level level, Cycle cycle) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الدورة
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: cycle.isLocked
                  ? Colors.grey.shade100
                  : AppColors.getLevelColor(level.id).withAlpha(30),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: cycle.isLocked
                        ? Colors.grey.shade300
                        : AppColors.getLevelColor(level.id),
                    shape: BoxShape.circle,
                  ),
                  child: cycle.isLocked
                      ? const Icon(Icons.lock, color: Colors.white, size: 16)
                      : Text(
                          '${cycle.id}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    cycle.title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: cycle.isLocked ? Colors.grey : Colors.black,
                    ),
                  ),
                ),
                if (!cycle.isLocked)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.getLevelColor(level.id).withAlpha(50),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${cycle.completedSentences}/${cycle.totalSentences}',
                      style: TextStyle(
                        color: AppColors.getLevelColor(level.id),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // مجموعات الدروس
          if (!cycle.isLocked) ...[
            // عنوان مجموعات الدروس
            Padding(
              padding: const EdgeInsets.only(
                  left: 12, right: 12, top: 12, bottom: 4),
              child: Row(
                children: [
                  const Icon(Icons.folder, size: 14, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'مجموعات الدروس (${cycle.lessonGroups.length})',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            // قائمة مجموعات الدروس
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: cycle.lessonGroups
                    .map((group) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: _buildLessonGroupItem(level, cycle, group),
                        ))
                    .toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // بناء عنصر مجموعة الدروس
  Widget _buildLessonGroupItem(Level level, Cycle cycle, LessonGroup group) {
    return InkWell(
      onTap: () => _navigateToLesson(level, group),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: group.isLocked ? Colors.grey.shade50 : Colors.blue.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: group.isLocked ? Colors.grey.shade300 : Colors.blue.shade200,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: group.isLocked
                    ? Colors.grey.shade300
                    : Colors.blue.shade100,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: group.isLocked
                    ? const Icon(Icons.lock, color: Colors.white, size: 12)
                    : Text(
                        '${group.id}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    group.title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: group.isLocked ? Colors.grey : Colors.black,
                    ),
                  ),
                  if (!group.isLocked) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          _getLessonTypeIcon(group.type),
                          size: 12,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getLessonTypeText(group.type),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
            Text(
              '${group.completedSentences}/${group.totalSentences}',
              style: TextStyle(
                fontSize: 12,
                color: group.isLocked ? Colors.grey : Colors.blue,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // الحصول على أيقونة نوع الدرس
  IconData _getLessonTypeIcon(LessonType type) {
    if (type == LessonType.sentenceBatch) {
      return Icons.format_list_bulleted;
    } else if (type == LessonType.conversation) {
      return Icons.chat;
    } else if (type == LessonType.review) {
      return Icons.refresh;
    } else {
      return Icons.article; // لن يتم الوصول إليه، ولكن مطلوب للتجميع
    }
  }

  // الحصول على نص نوع الدرس
  String _getLessonTypeText(LessonType type) {
    if (type == LessonType.sentenceBatch) {
      return 'دفعة جمل';
    } else if (type == LessonType.conversation) {
      return 'محادثة';
    } else if (type == LessonType.review) {
      return 'مراجعة';
    } else {
      return 'درس'; // لن يتم الوصول إليه، ولكن مطلوب للتجميع
    }
  }

  // الحصول على نص مستوى الصعوبة
  String _getDifficultyText(String difficulty) {
    switch (difficulty) {
      case 'easy':
        return 'سهل';
      case 'medium':
        return 'متوسط';
      case 'hard':
        return 'صعب';
      default:
        return 'متوسط';
    }
  }

  // الحصول على لون مستوى الصعوبة
  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return Colors.orange;
    }
  }

  void _navigateToLesson(Level level, LessonGroup group) {
    if (level.isLocked || group.isLocked) return;

    // التحقق مما إذا كانت المجموعة السابقة مكتملة
    bool canAccess = _canAccessGroup(level, group);
    if (!canAccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب إكمال المجموعة السابقة أولاً'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    // منع الوصول للدفعات المكتملة (باستثناء المراجعة)
    if (group.isCompleted && group.type == LessonType.sentenceBatch) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'تم إكمال ${group.title} بالفعل. يمكنك مراجعتها من صفحة المراجعة.'),
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: 'مراجعة',
            onPressed: () {
              Navigator.pushNamed(
                context,
                '/review',
                arguments: {
                  'levelId': level.id,
                  'cycleId': group.cycleId,
                  'groupId': group.id,
                },
              );
            },
          ),
        ),
      );
      return;
    }

    // التنقل إلى الصفحة المناسبة بناءً على نوع الدرس
    switch (group.type) {
      case LessonType.sentenceBatch:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DailySentencesScreen(
              levelId: level.id,
              cycleId: group.cycleId,
              groupId: group.id,
              title: group.title,
            ),
          ),
        );
        break;
      case LessonType.conversation:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ConversationScreen(
              levelId: level.id,
              cycleId: group.cycleId,
              groupId: group.id,
              title: group.title,
            ),
          ),
        );
        break;
      case LessonType.review:
        // التنقل إلى صفحة المراجعة مع تفعيل عرض المجموعات
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ReviewScreen(
              levelId: level.id,
              cycleId: group.cycleId,
              groupId: group.id,
              title: group.title,
              showGroups: true, // عرض بطاقات المجموعات بدلاً من الجمل مباشرة
            ),
          ),
        );
        break;
    }
  }

  // التحقق مما إذا كان يمكن الوصول إلى مجموعة معينة
  bool _canAccessGroup(Level level, LessonGroup group) {
    // المجموعة الأولى دائمًا يمكن الوصول إليها
    if (group.id == 1) return true;

    // البحث عن الدورة التي تنتمي إليها المجموعة
    final cycle = level.cycles.firstWhere(
      (c) => c.id == group.cycleId,
      orElse: () => level.cycles.first,
    );

    // البحث عن المجموعة السابقة
    final previousGroupId = group.id - 1;
    final previousGroup = cycle.lessonGroups.firstWhere(
      (g) => g.id == previousGroupId,
      orElse: () => LessonGroup(
        id: 0,
        cycleId: group.cycleId,
        globalId: 0,
        title: '',
        type: LessonType.sentenceBatch,
        totalSentences: 0,
        completedSentences: 0,
        accuracy: 0,
        isCompleted: true, // اعتبار المجموعة غير الموجودة مكتملة
        isLocked: false,
        routePath: null,
      ),
    );

    // يمكن الوصول إلى المجموعة فقط إذا كانت المجموعة السابقة مكتملة
    return previousGroup.isCompleted;
  }
}
